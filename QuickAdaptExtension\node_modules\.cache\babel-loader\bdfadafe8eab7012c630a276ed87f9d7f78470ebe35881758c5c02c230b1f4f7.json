{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\Tooltips\\\\TooltipBody.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useEffect, useRef, useState, useMemo } from \"react\";\nimport { Box, IconButton, Paper, Popover, Tooltip, Typography, LinearProgress, MobileStepper, Breadcrumbs } from \"@mui/material\";\nimport AddIcon from \"@mui/icons-material/Add\";\nimport { Image, TextFormat, Link } from \"@mui/icons-material\";\nimport useDrawerStore from \"../../store/drawerStore\";\nimport ButtonSection from \"./components/Buttons\";\nimport ImageSection from \"./components/ImageSection\";\nimport RTEsection from \"./components/RTE/RTESection\";\nimport { createPortal } from \"react-dom\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport JoditEditor from \"jodit-react\";\nimport AlertPopup from \"../drawer/AlertPopup\";\nimport PerfectScrollbar from 'react-perfect-scrollbar';\nimport \"react-perfect-scrollbar/dist/css/styles.css\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n// Maximum allowed sections of each type\nconst MAX_SECTIONS = {\n  image: 3,\n  button: 3,\n  rte: 3\n};\nconst TooltipBody = ({\n  isPopoverOpen,\n  setIsPopoverOpen,\n  popupPosition,\n  isUnSavedChanges,\n  openWarning,\n  setopenWarning,\n  handleLeave,\n  updatedGuideData\n}) => {\n  _s();\n  var _toolTipGuideMetaData4, _toolTipGuideMetaData5, _boxRef$current;\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    toolTipGuideMetaData,\n    currentStep,\n    handleTooltipRTEValue,\n    handleRTEDeleteSection,\n    handleRTECloneSection,\n    tooltip,\n    tooltipBackgroundcolor,\n    openTooltip,\n    selectedOption,\n    selectedTemplate,\n    selectedTemplateTour,\n    setTooltipPositionByXpath,\n    width,\n    borderRadius,\n    Annpadding,\n    borderColor,\n    tooltipborderradius,\n    tooltipbordersize,\n    tooltipBordercolor,\n    tooltipPosition,\n    tooltipWidth,\n    tooltippadding,\n    AnnborderSize,\n    currentStepIndex,\n    dismissData,\n    steps,\n    setCurrentHoveredElement,\n    elementClick,\n    dismiss,\n    setDismiss,\n    progress,\n    setProgress,\n    ProgressColor,\n    setProgressColor\n  } = useDrawerStore(state => state);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const boxRef = useRef(new Map());\n  const [savedRange, setSaveRange] = useState(undefined);\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [currentRTEFocusedId, setCurrentRTEFocusedId] = useState(\"\");\n  const [currentFocusedType, setCurrentFocusedType] = useState();\n  const [isEditorFocused, setIsEditorFocused] = useState(false);\n  const [rteToolbarVisible, setRteToolbarVisible] = useState({});\n\n  // State to track if scrolling is needed\n  const [needsScrolling, setNeedsScrolling] = useState(false);\n  const contentRef = useRef(null);\n  const scrollbarRef = useRef(null);\n\n  // Section count tracking for limits\n  const [sectionCounts, setSectionCounts] = useState({\n    image: 0,\n    button: 0,\n    rte: 0\n  });\n\n  // Helper function to check if a section type has reached its limit\n  const hasReachedLimit = type => {\n    // Map the section type to the corresponding key in sectionCounts\n    const countType = type === \"rte\" ? \"rte\" : type === \"button\" ? \"button\" : type === \"image\" ? \"image\" : null;\n\n    // If the type is not supported, return false\n    if (countType === null) return false;\n    return sectionCounts[countType] >= MAX_SECTIONS[countType];\n  };\n  const handleAddIconClick = (event, idx) => {\n    if (hasReachedLimit(\"rte\") && hasReachedLimit(\"button\") && hasReachedLimit(\"image\")) {\n      return;\n    }\n    const currentTarget = event.currentTarget;\n    setTimeout(() => {\n      setCurrentIndex(idx);\n      setAnchorEl(currentTarget);\n    }, 0);\n  };\n  const handlePopoverClose = () => {\n    setAnchorEl(null);\n  };\n  const handleFocus = id => {\n    setIsPopoverOpen(true);\n    setCurrentRTEFocusedId(id);\n  };\n  const handleBlur = id => {\n    var _boxRef$current$get, _boxRef$current$get$c;\n    if (boxRef !== null && boxRef !== void 0 && (_boxRef$current$get = boxRef.current.get(id)) !== null && _boxRef$current$get !== void 0 && (_boxRef$current$get$c = _boxRef$current$get.current) !== null && _boxRef$current$get$c !== void 0 && _boxRef$current$get$c.innerHTML) {\n      var _boxRef$current$get2, _boxRef$current$get2$;\n      handleTooltipRTEValue(id, ((_boxRef$current$get2 = boxRef.current.get(id)) === null || _boxRef$current$get2 === void 0 ? void 0 : (_boxRef$current$get2$ = _boxRef$current$get2.current) === null || _boxRef$current$get2$ === void 0 ? void 0 : _boxRef$current$get2$.innerHTML.trim()) || \"\");\n    }\n  };\n  const handleDeleteSection = () => {\n    handleRTEDeleteSection(currentRTEFocusedId);\n  };\n  const handleCloneContainer = () => {\n    handleRTECloneSection(currentRTEFocusedId);\n  };\n  const [sections, setSections] = useState([{\n    type: \"image\"\n  }, {\n    type: \"text\"\n  }, {\n    type: \"button\"\n  }]);\n  const [draggingIndex, setDraggingIndex] = useState(null);\n  const [draggedIndex, setDraggedIndex] = useState(null);\n  const handleDragStart = index => {\n    setDraggingIndex(index);\n  };\n  const handleDragEnter = index => {\n    if (draggingIndex !== null && draggingIndex !== index) {\n      const reorderedSections = [...sections];\n      const [removed] = reorderedSections.splice(draggingIndex, 1);\n      reorderedSections.splice(index, 0, removed);\n      setSections(reorderedSections);\n      setDraggingIndex(index);\n    }\n  };\n  const handleDragEnd = () => {\n    setDraggingIndex(null);\n  };\n  const addToBoxRef = id => {\n    if (!boxRef.current.has(id)) {\n      const newRef = /*#__PURE__*/React.createRef();\n      boxRef.current.set(id, newRef);\n      return newRef;\n    }\n    return boxRef.current.get(id);\n  };\n\n  // Update section counts when the component mounts or when containers change\n  useEffect(() => {\n    var _toolTipGuideMetaData;\n    if (toolTipGuideMetaData && (_toolTipGuideMetaData = toolTipGuideMetaData[currentStep - 1]) !== null && _toolTipGuideMetaData !== void 0 && _toolTipGuideMetaData.containers) {\n      const containers = toolTipGuideMetaData[currentStep - 1].containers;\n      const counts = {\n        image: 0,\n        button: 0,\n        rte: 0\n      };\n\n      // Count each type of section\n      containers.forEach(container => {\n        if (container.type === \"image\") counts.image++;else if (container.type === \"button\") counts.button++;else if (container.type === \"rte\") counts.rte++;\n      });\n      setSectionCounts(counts);\n    }\n  }, [toolTipGuideMetaData, currentStep]);\n\n  // Check if content needs scrolling with improved detection\n  useEffect(() => {\n    const checkScrollNeeded = () => {\n      if (contentRef.current) {\n        // Force a reflow to get accurate measurements\n        contentRef.current.style.height = 'auto';\n        const contentHeight = contentRef.current.scrollHeight;\n        const containerHeight = 320; // max-height value\n        const shouldScroll = contentHeight > containerHeight;\n        setNeedsScrolling(shouldScroll);\n\n        // Force update scrollbar\n        if (scrollbarRef.current) {\n          // Try multiple methods to update the scrollbar\n          if (scrollbarRef.current.updateScroll) {\n            scrollbarRef.current.updateScroll();\n          }\n          // Force re-initialization if needed\n          setTimeout(() => {\n            if (scrollbarRef.current && scrollbarRef.current.updateScroll) {\n              scrollbarRef.current.updateScroll();\n            }\n          }, 10);\n        }\n      }\n    };\n    checkScrollNeeded();\n    const timeouts = [setTimeout(checkScrollNeeded, 50), setTimeout(checkScrollNeeded, 100), setTimeout(checkScrollNeeded, 200), setTimeout(checkScrollNeeded, 500)];\n    let resizeObserver = null;\n    let mutationObserver = null;\n    if (contentRef.current && window.ResizeObserver) {\n      resizeObserver = new ResizeObserver(() => {\n        setTimeout(checkScrollNeeded, 10);\n      });\n      resizeObserver.observe(contentRef.current);\n    }\n    if (contentRef.current && window.MutationObserver) {\n      mutationObserver = new MutationObserver(() => {\n        setTimeout(checkScrollNeeded, 10);\n      });\n      mutationObserver.observe(contentRef.current, {\n        childList: true,\n        subtree: true,\n        attributes: true,\n        attributeFilter: ['style', 'class']\n      });\n    }\n    return () => {\n      timeouts.forEach(clearTimeout);\n      if (resizeObserver) {\n        resizeObserver.disconnect();\n      }\n      if (mutationObserver) {\n        mutationObserver.disconnect();\n      }\n    };\n  }, [toolTipGuideMetaData, currentStep]);\n  useEffect(() => {\n    if (openTooltip) {\n      if (typeof window !== \"undefined\") {\n        var _toolTipGuideMetaData2, _toolTipGuideMetaData3;\n        const xpath = (_toolTipGuideMetaData2 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData2 === void 0 ? void 0 : (_toolTipGuideMetaData3 = _toolTipGuideMetaData2.xpath) === null || _toolTipGuideMetaData3 === void 0 ? void 0 : _toolTipGuideMetaData3.value;\n\n        // Check if XPath is valid and not empty\n        if (xpath && xpath.trim() !== \"\") {\n          const result = window.document.evaluate(xpath, window.document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);\n          const element = result.singleNodeValue;\n          if (element) {\n            var _element$textContent;\n            setCurrentHoveredElement(element);\n            const rect = element.getBoundingClientRect();\n            const info = {\n              element,\n              tagName: element.tagName,\n              classes: element.className,\n              id: element.id,\n              position: {\n                x: rect.x,\n                y: rect.y,\n                width: rect.width,\n                height: rect.height\n              },\n              textContent: ((_element$textContent = element.textContent) === null || _element$textContent === void 0 ? void 0 : _element$textContent.trim()) || \"\"\n            };\n            setTooltipPositionByXpath(info);\n          }\n        }\n      }\n      setTimeout(() => {\n        let popup = document.querySelector(\".MuiTooltip-popperInteractive\");\n        if (popup instanceof HTMLElement && popupPosition) {\n          //popup.style.top = `${popupPosition.top || \"20px\"}`;\n          //popup.style.left = `${popupPosition.left || \"10px\"}`; // Example for setting the left position\n        }\n      }, 10);\n    }\n  }, [openTooltip]);\n  const canvasProperties = (_toolTipGuideMetaData4 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData4 === void 0 ? void 0 : _toolTipGuideMetaData4.canvas;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        placeContent: \"end\",\n        display: \"flex\"\n      },\n      children: !isPopoverOpen && dismiss && /*#__PURE__*/_jsxDEV(IconButton, {\n        className: \"qadpt-dismiss\",\n        \"aria-label\": \"close\",\n        children: /*#__PURE__*/_jsxDEV(CloseIcon, {\n          sx: {\n            zoom: \"1\",\n            color: \"#000\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 6\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 339,\n      columnNumber: 3\n    }, this), /*#__PURE__*/_jsxDEV(PerfectScrollbar, {\n      ref: scrollbarRef,\n      style: {\n        maxHeight: \"320px\"\n      },\n      options: {\n        suppressScrollY: !needsScrolling,\n        suppressScrollX: true,\n        wheelPropagation: false,\n        swipeEasing: true,\n        minScrollbarLength: 20,\n        scrollingThreshold: 1000,\n        scrollYMarginOffset: 0\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: contentRef,\n        style: {\n          // backgroundColor: canvasProperties?.backgroundColor || \"white\",\n          // padding: canvasProperties?.padding || \"4px\",\n          // width: `${toolTipGuideMetaData[currentStep - 1]?.canvas.width} !important` || \"500px !important\",\n          // borderRadius: canvasProperties?.borderRadius || \"8px\",\n          // minWidth: TOOLTIP_MN_WIDTH,\n          // maxWidth: TOOLTIP_MX_WIDTH,\n          minHeight: 60,\n          // maxHeight: 320,\n          // height: selectedTemplate === \"Tooltip\" ? \"auto\" : \"350px\",\n          overflow: \"hidden\",\n          position: \"relative\"\n        },\n        children: [(_toolTipGuideMetaData5 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData5 === void 0 ? void 0 : _toolTipGuideMetaData5.containers.map((item, index) => {\n          const id = `${item.type}|${item.id}`;\n          const handleDragStart = index => {\n            if (draggedIndex === index) return; // Prevent redundant drag starts\n            setDraggedIndex(index);\n          };\n          const handleDragEnter = index => {\n            if (draggedIndex !== null && draggedIndex !== index) {\n              const updatedContainers = [...toolTipGuideMetaData[currentStep - 1].containers];\n              const [draggedItem] = updatedContainers.splice(draggedIndex, 1);\n              updatedContainers.splice(index, 0, draggedItem);\n\n              // Update state only if the order has changed\n              useDrawerStore.setState({\n                toolTipGuideMetaData: [{\n                  ...toolTipGuideMetaData[currentStep - 1],\n                  containers: updatedContainers\n                }]\n              });\n              setDraggedIndex(index);\n            }\n          };\n          const handleDragEnd = () => {\n            setDraggedIndex(null);\n          };\n          return /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              onMouseEnter: e => {\n                setCurrentFocusedType(e.currentTarget.id);\n              },\n              style: {\n                position: \"relative\",\n                padding: \"7px 0\",\n                borderBottomWidth: currentFocusedType === id ? \"1px\" : \"0px\",\n                borderBottomColor: currentFocusedType === id ? \"var(--primarycolor)\" : \"transparent\",\n                borderBottomStyle: currentFocusedType === id ? \"dotted\" : \"none\",\n                marginBottom: \"10px\"\n              },\n              id: id,\n              draggable: item.type !== \"rte\",\n              onDragStart: () => handleDragStart(index),\n              onDragEnter: () => handleDragEnter(index),\n              onDragEnd: handleDragEnd,\n              children: item.type === \"button\" ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(ButtonSection, {\n                  items: item,\n                  updatedGuideData: updatedGuideData,\n                  isCloneDisabled: hasReachedLimit(\"button\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 10\n                }, this), currentFocusedType === id && /*#__PURE__*/_jsxDEV(AddSectionComp, {\n                  handleAddIconClick: e => handleAddIconClick(e, index)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 11\n                }, this)]\n              }, void 0, true) : item.type === \"image\" ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(ImageSection, {\n                  items: item,\n                  isCloneDisabled: hasReachedLimit(\"image\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 10\n                }, this), currentFocusedType === id && /*#__PURE__*/_jsxDEV(AddSectionComp, {\n                  handleAddIconClick: e => handleAddIconClick(e, index)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 11\n                }, this)]\n              }, void 0, true) : item.type === \"rte\" ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(RTEsection, {\n                  items: item\n                  //@ts-ignore\n                  ,\n                  boxRef: addToBoxRef(item.id),\n                  handleFocus: handleFocus,\n                  handleeBlur: handleBlur,\n                  isPopoverOpen: isPopoverOpen,\n                  setIsPopoverOpen: setIsPopoverOpen,\n                  currentRTEFocusedId: currentRTEFocusedId,\n                  isCloneDisabled: hasReachedLimit(\"rte\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 10\n                }, this), currentFocusedType === id && /*#__PURE__*/_jsxDEV(AddSectionComp, {\n                  handleAddIconClick: e => handleAddIconClick(e, index)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 466,\n                  columnNumber: 11\n                }, this)]\n              }, void 0, true) : null\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 7\n            }, this)\n          }, void 0, false);\n        }), /*#__PURE__*/_jsxDEV(SectionPopOver, {\n          anchorEl: anchorEl,\n          handlePopoverClose: handlePopoverClose,\n          currentIndex: currentIndex,\n          hasReachedLimit: hasReachedLimit\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 4\n        }, this), isUnSavedChanges && openWarning && /*#__PURE__*/_jsxDEV(AlertPopup, {\n          openWarning: openWarning,\n          setopenWarning: setopenWarning,\n          handleLeave: handleLeave\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 486,\n          columnNumber: 5\n        }, this), isPopoverOpen && currentRTEFocusedId && (boxRef === null || boxRef === void 0 ? void 0 : (_boxRef$current = boxRef.current) === null || _boxRef$current === void 0 ? void 0 : _boxRef$current.get(currentRTEFocusedId)) && /*#__PURE__*/_jsxDEV(RTEToolbar, {\n          isPopoverOpen: isPopoverOpen,\n          setIsPopoverOpen: setIsPopoverOpen,\n          boxRef: boxRef.current.get(currentRTEFocusedId),\n          currentRTEFocusedId: currentRTEFocusedId,\n          handleDeleteSection: handleDeleteSection,\n          handleCloneContainer: handleCloneContainer,\n          savedRange: savedRange,\n          setSaveRange: setSaveRange,\n          tooltip: tooltip,\n          popupPosition: popupPosition,\n          handleTooltipRTEValue: handleTooltipRTEValue,\n          iseditorfocused: isEditorFocused,\n          setIsEditorFocused: setIsEditorFocused,\n          canvasproperties: canvasProperties,\n          isCloneDisabled: hasReachedLimit(\"rte\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 5\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 3\n      }, this)\n    }, `scrollbar-${needsScrolling}`, false, {\n      fileName: _jsxFileName,\n      lineNumber: 350,\n      columnNumber: 4\n    }, this), progress && toolTipGuideMetaData.length > 1 && (selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\") && (selectedOption === 1 || selectedOption === \"\" ? /*#__PURE__*/_jsxDEV(DotsStepper, {\n      activeStep: currentStep,\n      steps: steps.length,\n      ProgressColor: ProgressColor\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 516,\n      columnNumber: 6\n    }, this) : selectedOption === 2 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(LinearProgress, {\n        variant: \"determinate\"\n        //sx={{paddingTop:\"15px\",padding:\"8px\"}}\n        ,\n        value: currentStep / steps.length * 100,\n        sx: {\n          height: \"6px\",\n          borderRadius: \"20px\",\n          margin: \"6px 10px\",\n          '& .MuiLinearProgress-bar': {\n            backgroundColor: ProgressColor // progress bar color\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 523,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 522,\n      columnNumber: 6\n    }, this) : selectedOption === 3 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: \"8px\"\n      },\n      children: /*#__PURE__*/_jsxDEV(BreadCrumpStepper, {\n        activeStep: currentStep,\n        steps: steps.length,\n        ProgressColor: ProgressColor\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 539,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 538,\n      columnNumber: 6\n    }, this) : selectedOption === 4 ? /*#__PURE__*/_jsxDEV(Breadcrumbs, {\n      \"aria-label\": \"breadcrumb\",\n      sx: {\n        padding: \"8px\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          color: ProgressColor\n        },\n        children: [\"Step \", currentStep, \" of \", steps.length]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 551,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 547,\n      columnNumber: 6\n    }, this) : null)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 338,\n    columnNumber: 3\n  }, this);\n};\n_s(TooltipBody, \"49wu2KJsi9BbLhyJp8+0vhxKDjE=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = TooltipBody;\nexport default TooltipBody;\nconst AddSectionComp = ({\n  handleAddIconClick\n}) => {\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      position: \"absolute\",\n      transform: \"translate(-50%,-50%)\",\n      top: \"99%\",\n      left: \"50%\"\n    },\n    children: /*#__PURE__*/_jsxDEV(IconButton, {\n      onClick: handleAddIconClick,\n      sx: {\n        backgroundColor: \"#5F9EA0\",\n        \"&:hover\": {\n          backgroundColor: \"#70afaf\"\n        },\n        borderRadius: \"8px\",\n        padding: \"5px !important\"\n      },\n      children: /*#__PURE__*/_jsxDEV(AddIcon, {\n        fontSize: \"small\",\n        sx: {\n          color: \"#fff\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 579,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 568,\n      columnNumber: 4\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 567,\n    columnNumber: 3\n  }, this);\n};\n_c2 = AddSectionComp;\nconst SectionPopOver = ({\n  anchorEl,\n  handlePopoverClose,\n  currentIndex,\n  hasReachedLimit\n}) => {\n  _s2();\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    createTooltipSections,\n    selectedOption,\n    currentStep,\n    steps,\n    currentStepIndex\n  } = useDrawerStore(state => state);\n  const handleAddSection = sectionType => {\n    // Don't add if limit is reached\n    if (hasReachedLimit(sectionType)) {\n      return;\n    }\n    createTooltipSections(sectionType, currentIndex);\n    handlePopoverClose();\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: anchorEl && /*#__PURE__*/_jsxDEV(Popover, {\n      open: Boolean(anchorEl),\n      anchorEl: anchorEl,\n      onClose: handlePopoverClose,\n      anchorOrigin: {\n        vertical: \"bottom\",\n        horizontal: \"center\"\n      },\n      transformOrigin: {\n        vertical: \"top\",\n        horizontal: \"center\"\n      },\n      id: \"tooltip-section-popover\",\n      slotProps: {\n        paper: {\n          sx: {\n            padding: \"12px\",\n            display: \"flex\",\n            gap: \"16px\",\n            width: \"auto\"\n          }\n        },\n        root: {\n          // instead of writing sx on popover write here it also target to root and more clear\n          sx: {\n            zIndex: theme => theme.zIndex.tooltip + 1000\n          }\n        }\n      },\n      disableEnforceFocus: true,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        flexDirection: \"row\",\n        gap: \"16px\",\n        children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n          arrow: true,\n          title: hasReachedLimit(\"rte\") ? translate(\"Maximum limit of 3 Rich Text sections reached\", {\n            defaultValue: \"Maximum limit of 3 Rich Text sections reached\"\n          }) : \"\",\n          PopperProps: {\n            sx: {\n              zIndex: 9999\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: \"center\",\n            sx: {\n              cursor: hasReachedLimit(\"rte\") ? \"not-allowed\" : \"pointer\",\n              opacity: hasReachedLimit(\"rte\") ? 0.5 : 1,\n              svg: {\n                fontSize: \"24px !important\"\n              }\n            },\n            onClick: () => !hasReachedLimit(\"rte\") && handleAddSection(\"rte\"),\n            children: [/*#__PURE__*/_jsxDEV(TextFormat, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 674,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                fontSize: \"11px !important\"\n              },\n              children: translate(\"Rich Text\", {\n                defaultValue: \"Rich Text\"\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 675,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 661,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 653,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          arrow: true,\n          title: hasReachedLimit(\"button\") ? translate(\"Maximum limit of 3 Button sections reached\", {\n            defaultValue: \"Maximum limit of 3 Button sections reached\"\n          }) : \"\",\n          PopperProps: {\n            sx: {\n              zIndex: 9999\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: \"center\",\n            sx: {\n              cursor: hasReachedLimit(\"button\") ? \"not-allowed\" : \"pointer\",\n              opacity: hasReachedLimit(\"button\") ? 0.5 : 1,\n              svg: {\n                fontSize: \"24px !important\"\n              }\n            },\n            onClick: () => !hasReachedLimit(\"button\") && handleAddSection(\"button\"),\n            children: [/*#__PURE__*/_jsxDEV(Link, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 700,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                fontSize: \"11px !important\"\n              },\n              children: translate(\"Button\", {\n                defaultValue: \"Button\"\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 701,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 687,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 679,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          arrow: true,\n          title: hasReachedLimit(\"image\") ? translate(\"Maximum limit of 3 Image sections reached\", {\n            defaultValue: \"Maximum limit of 3 Image sections reached\"\n          }) : \"\",\n          PopperProps: {\n            sx: {\n              zIndex: 9999\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: \"center\",\n            sx: {\n              cursor: hasReachedLimit(\"image\") ? \"not-allowed\" : \"pointer\",\n              opacity: hasReachedLimit(\"image\") ? 0.5 : 1,\n              svg: {\n                fontSize: \"24px !important\"\n              }\n            },\n            onClick: () => !hasReachedLimit(\"image\") && handleAddSection(\"image\"),\n            children: [/*#__PURE__*/_jsxDEV(Image, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 726,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                fontSize: \"11px !important\"\n              },\n              children: translate(\"Image\", {\n                defaultValue: \"Image\"\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 713,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 705,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 648,\n        columnNumber: 6\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 617,\n      columnNumber: 5\n    }, this)\n  }, void 0, false);\n};\n_s2(SectionPopOver, \"xK2DzNEhobRJvmNjAuxK0PF4TV8=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c3 = SectionPopOver;\nconst RTEToolbar = ({\n  isPopoverOpen,\n  currentRTEFocusedId,\n  tooltip,\n  setIsPopoverOpen,\n  boxRef,\n  savedRange,\n  handleDeleteSection,\n  handleCloneContainer,\n  setSaveRange,\n  popupPosition,\n  handleTooltipRTEValue,\n  iseditorfocused,\n  setIsEditorFocused,\n  canvasproperties,\n  isCloneDisabled\n}) => {\n  _s3();\n  var _document$getElementB, _document$getElementB2, _document$getElementB3, _document$getElementB4, _document$getElementB5, _boxRef$current2;\n  // const handleRTEPopoverClose = (event: MouseEvent | TouchEvent) => {\n  // \tconst target = event.target as HTMLElement;\n\n  // \t// Check if the target is within specific editable or toolbar elements\n  // \tconst isElement =\n  // \t\ttarget.closest(\"[contenteditable]\") ||\n  // \t\ttarget.closest(\"#rte-toolbar-paper\") ||\n  // \t\ttarget.closest(\"#rte-alignment-menu\") ||\n  // \t\ttarget.closest(\"#rte-alignment-menu-items\") ||\n  // \t\ttarget.closest(target.id.startsWith(\"#rt-editor\") ? `${target.id}` : \"nope\");\n  // \tif (target && isElement) {\n  // \t\treturn;\n  // \t}\n\n  // \t// Check if the target is within Jodit Editor container\n  // \t//const isJoditContainer = target.classList.contains(\"backdrop\");\n  // \t//const isClickedoutside =  target.classList.contains(\"quickAdopt-selection\");\n\n  // \t// Handle dynamic ID checks safely\n  // \t// const isRTEditor = target.id.startsWith(\"rt-editor\") && target.closest(`#${target.id}`);\n\n  // \t// if (isJoditContainer || isElement || isRTEditor) {\n  // \t// \treturn;\n  // \t// }\n\n  // \t// Delay closing the popover slightly\n\n  // \t\tsetIsPopoverOpen(false);\n\n  // };\n\n  // const handleBlur = () => {\n  // \tsetIsEditorFocused(false);\n  //     console.log(\"Editor lost focus\");\n  // };\n\n  const handleFocus = () => {\n    setIsEditorFocused(true);\n    console.log(\"Editor is focused\");\n  };\n  const [isRtlDirection, setIsRtlDirection] = useState(false);\n  useEffect(() => {\n    const dir = document.body.getAttribute(\"dir\") || \"ltr\";\n    setIsRtlDirection(dir.toLowerCase() === \"rtl\");\n  }, []);\n  const editorConfig = useMemo(() => ({\n    direction: isRtlDirection ? 'rtl' : 'ltr',\n    // Jodit uses 'direction' not just 'rtl'\n    language: 'en',\n    // Optional: change language as well\n    toolbarSticky: false,\n    toolbarAdaptive: false,\n    autofocus: true,\n    // Enable auto-focus for immediate interaction\n    buttons: [\"bold\", \"italic\", \"underline\", \"brush\", \"font\", \"fontsize\", \"link\", {\n      name: \"more\",\n      iconURL: \"https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg\",\n      list: [\"source\", \"strikethrough\", \"ul\", \"ol\", \"image\", \"video\", \"table\", \"align\", \"undo\", \"redo\", \"|\", \"hr\", \"eraser\", \"copyformat\", \"symbol\", \"fullsize\", \"print\", \"superscript\", \"subscript\", \"|\", \"outdent\", \"indent\", \"paragraph\"]\n    }],\n    events: {\n      focus: handleFocus,\n      //blur: handleBlur\n      afterInit: editor => {\n        // Ensure the editor is focused immediately after initialization\n        setTimeout(() => {\n          editor.focus();\n        }, 0);\n      }\n    },\n    maxHeight: \"calc(100% - 90px)\"\n  }), [isRtlDirection]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: isPopoverOpen && /*#__PURE__*/createPortal(\n    /*#__PURE__*/\n    // <ClickAwayListener onClickAway={(event) => {\n    // \tconst target = event.target as HTMLElement;\n    // \t// const isJoditContainer = target.classList.contains(\"backdrop\");\n    // \t// const isClickedoutside = target.classList.contains(\"quickAdopt-selection\");\n    // \tconst isInsidePopup = document.querySelector(\".jodit-popup\")?.contains(event.target as Node);\n    // \tconst isInsideJoditPopup = document.querySelector(\".jodit-wysiwyg\")?.contains(event.target as Node);\n    // \tconst isInsideWorkplacePopup = isInsideJoditPopup || document.querySelector(\".jodit-dialog__panel\")?.contains(event.target as Node);\n    // \tconst isSelectionMarker = (event.target as HTMLElement).id.startsWith(\"jodit-selection_marker_\");\n    // \tconst isLinkPopup = document.querySelector(\".jodit-ui-input__input\")?.contains(event.target as Node);\n    // \t// Check if the target is inside a button, and if it is, check if it's inside the toolbar button container\n    // \tconst isInsideToolbarButton = (event.target as HTMLElement).closest(\".jodit-toolbar-button__button\") !== null;\n    // \t// Check if the target is the specific \"Insert\" button (or similar button you want)\n    // \tconst isInsertButton = (event.target as HTMLElement).closest(\"button[aria-pressed='false']\") !== null;\n    // \tif (!isInsidePopup && !isInsideWorkplacePopup && !isSelectionMarker && !isLinkPopup && !isInsideToolbarButton && !isInsertButton) {\n    // \t\thandleRTEPopoverClose(event);\n    // \t}\n    // }}>\n    _jsxDEV(Paper, {\n      style: {\n        position: \"absolute\",\n        zIndex: 99999,\n        left: ((_document$getElementB = (_document$getElementB2 = document.getElementById(\"Tooltip-unique\")) === null || _document$getElementB2 === void 0 ? void 0 : (_document$getElementB3 = _document$getElementB2.getBoundingClientRect()) === null || _document$getElementB3 === void 0 ? void 0 : _document$getElementB3.x) !== null && _document$getElementB !== void 0 ? _document$getElementB : 150) + 3,\n        // @ts-ignore\n        top: ((_document$getElementB4 = document.getElementById(\"Tooltip-unique\")) === null || _document$getElementB4 === void 0 ? void 0 : (_document$getElementB5 = _document$getElementB4.getBoundingClientRect()) === null || _document$getElementB5 === void 0 ? void 0 : _document$getElementB5.y) - 50 || 80,\n        width: (canvasproperties === null || canvasproperties === void 0 ? void 0 : canvasproperties.width) || \"300px\"\n      },\n      component: \"div\",\n      id: \"rte-toolbar-paper\",\n      role: \"presentation\",\n      sx: {\n        \"& .jodit-status-bar-link\": {\n          display: \"none !important\"\n        },\n        \"& .jodit-ui-input__wrapper\": {\n          pointerEvents: \"auto\"\n        },\n        \"& .jodit-toolbar-button button\": {\n          minWidth: \"8px !important\"\n        },\n        \"& .jodit-workplace\": {\n          maxHeight: \"calc(100vh - 290px) !important\",\n          overflowX: \"auto !important\"\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(JoditEditor, {\n        value: (boxRef === null || boxRef === void 0 ? void 0 : (_boxRef$current2 = boxRef.current) === null || _boxRef$current2 === void 0 ? void 0 : _boxRef$current2.innerHTML) || \"\",\n        className: \"qadpt-jodit\",\n        config: {\n          ...editorConfig,\n          controls: {\n            font: {\n              list: {\n                \"Poppins, sans-serif\": \"Poppins\",\n                \"Roboto, sans-serif\": \"Roboto\",\n                \"Comic Sans MS, sans-serif\": \"Comic Sans MS\",\n                \"Open Sans, sans-serif\": \"Open Sans\",\n                \"Calibri, sans-serif\": \"Calibri\",\n                \"Century Gothic, sans-serif\": \"Century Gothic\"\n              }\n            }\n          }\n        },\n        onBlur: (newContent, event) => {\n          var _document$querySelect, _document$querySelect2, _document$querySelect3, _document$querySelect4;\n          const isInsidePopup = (_document$querySelect = document.querySelector(\".jodit-popup\")) === null || _document$querySelect === void 0 ? void 0 : _document$querySelect.contains(event.target);\n          const isInsideJoditPopup = (_document$querySelect2 = document.querySelector(\".jodit-wysiwyg\")) === null || _document$querySelect2 === void 0 ? void 0 : _document$querySelect2.contains(event.target);\n          const isInsideWorkplacePopup = (_document$querySelect3 = document.querySelector(\".jodit-dialog__panel\")) === null || _document$querySelect3 === void 0 ? void 0 : _document$querySelect3.contains(event.target);\n          const isSelectionMarker = event.target.id.startsWith(\"jodit-selection_marker_\");\n          const isLinkPopup = (_document$querySelect4 = document.querySelector(\".jodit-ui-input__input\")) === null || _document$querySelect4 === void 0 ? void 0 : _document$querySelect4.contains(event.target);\n          // Check if the target is inside a button, and if it is, check if it's inside the toolbar button container\n          const isInsideToolbarButton = event.target.closest(\".jodit-toolbar-button__button\") !== null;\n          const isPasteEvent = event.type === \"paste\" || event.type === \"keydown\" && event.ctrlKey && event.key === \"v\";\n          // Check if the target is the specific \"Insert\" button (or similar button you want)\n          const isInsertButton = event.target.closest(\"button[aria-pressed='false']\") !== null;\n          const formElement = document.querySelector(\".jodit-ui-form\");\n          if (boxRef !== null && boxRef !== void 0 && boxRef.current && !isInsidePopup && !isSelectionMarker && !isLinkPopup && !isInsideToolbarButton && !isInsertButton && !formElement && isInsideWorkplacePopup === undefined) {\n            boxRef.current.innerHTML = newContent;\n            handleTooltipRTEValue(currentRTEFocusedId, newContent.trim());\n            setIsPopoverOpen(false);\n          }\n        },\n        onChange: newContent => {\n          if (boxRef !== null && boxRef !== void 0 && boxRef.current) {\n            boxRef.current.innerHTML = newContent;\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 980,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 952,\n      columnNumber: 6\n    }, this),\n    // </ClickAwayListener >\n    document.body, \"rte-toolbar-portal\")\n  }, void 0, false);\n};\n_s3(RTEToolbar, \"erbqNrqJyXI/2f3ErHuzYSYDE0o=\");\n_c4 = RTEToolbar;\nconst DotsStepper = ({\n  steps,\n  activeStep,\n  ProgressColor\n}) => {\n  return /*#__PURE__*/_jsxDEV(MobileStepper, {\n    variant: \"dots\",\n    steps: steps,\n    position: \"static\",\n    activeStep: activeStep - 1,\n    sx: {\n      flexGrow: 1,\n      display: \"flex\",\n      justifyContent: \"center\",\n      background: \"inherit\",\n      \"& .MuiMobileStepper-dotActive\": {\n        backgroundColor: ProgressColor // active dot color\n      }\n    },\n    nextButton: /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false),\n    backButton: /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1049,\n    columnNumber: 3\n  }, this);\n};\n_c5 = DotsStepper;\nconst BreadCrumpStepper = ({\n  steps,\n  activeStep,\n  ProgressColor\n}) => {\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      justifyContent: 'center',\n      gap: \"4px\" // Adjust space between steps\n    },\n    children: Array.from({\n      length: steps\n    }).map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        width: '14px',\n        height: '4px',\n        backgroundColor: index === activeStep - 1 ? ProgressColor : '#e0e0e0',\n        // Active color and inactive color\n        borderRadius: '100px'\n      }\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1073,\n      columnNumber: 6\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1064,\n    columnNumber: 3\n  }, this);\n};\n_c6 = BreadCrumpStepper;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"TooltipBody\");\n$RefreshReg$(_c2, \"AddSectionComp\");\n$RefreshReg$(_c3, \"SectionPopOver\");\n$RefreshReg$(_c4, \"RTEToolbar\");\n$RefreshReg$(_c5, \"DotsStepper\");\n$RefreshReg$(_c6, \"BreadCrumpStepper\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "useMemo", "Box", "IconButton", "Paper", "Popover", "<PERSON><PERSON><PERSON>", "Typography", "LinearProgress", "MobileStepper", "Breadcrumbs", "AddIcon", "Image", "TextFormat", "Link", "useDrawerStore", "ButtonSection", "ImageSection", "RTEsection", "createPortal", "CloseIcon", "JoditEditor", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PerfectScrollbar", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MAX_SECTIONS", "image", "button", "rte", "TooltipBody", "isPopoverOpen", "setIsPopoverOpen", "popupPosition", "isUnSavedChanges", "openWarning", "setopenWarning", "handleLeave", "updatedGuideData", "_s", "_toolTipGuideMetaData4", "_toolTipGuideMetaData5", "_boxRef$current", "t", "translate", "toolTipGuideMetaData", "currentStep", "handleTooltipRTEValue", "handleRTEDeleteSection", "handleRTECloneSection", "tooltip", "tooltipBackgroundcolor", "openTooltip", "selectedOption", "selectedTemplate", "selectedTemplateTour", "setTooltipPositionByXpath", "width", "borderRadius", "Annpadding", "borderColor", "tooltipborderradius", "tooltipbordersize", "tooltipBordercolor", "tooltipPosition", "tooltipWidth", "tooltippadding", "AnnborderSize", "currentStepIndex", "dismissData", "steps", "setCurrentHoveredElement", "elementClick", "dismiss", "<PERSON><PERSON><PERSON><PERSON>", "progress", "setProgress", "ProgressColor", "setProgressColor", "state", "anchorEl", "setAnchorEl", "boxRef", "Map", "savedRange", "setSaveRange", "undefined", "currentIndex", "setCurrentIndex", "currentRTEFocusedId", "setCurrentRTEFocusedId", "currentFocusedType", "setCurrentFocusedType", "isEditorFocused", "setIsEditorFocused", "rteToolbarVisible", "setRteToolbarVisible", "needsScrolling", "setNeedsScrolling", "contentRef", "scrollbarRef", "sectionCounts", "setSectionCounts", "hasReachedLimit", "type", "countType", "handleAddIconClick", "event", "idx", "currentTarget", "setTimeout", "handlePopoverClose", "handleFocus", "id", "handleBlur", "_boxRef$current$get", "_boxRef$current$get$c", "current", "get", "innerHTML", "_boxRef$current$get2", "_boxRef$current$get2$", "trim", "handleDeleteSection", "handleCloneContainer", "sections", "setSections", "draggingIndex", "setDraggingIndex", "draggedIndex", "setDraggedIndex", "handleDragStart", "index", "handleDragEnter", "reorderedSections", "removed", "splice", "handleDragEnd", "addToBoxRef", "has", "newRef", "createRef", "set", "_toolTipGuideMetaData", "containers", "counts", "for<PERSON>ach", "container", "checkScrollNeeded", "style", "height", "contentHeight", "scrollHeight", "containerHeight", "shouldScroll", "updateScroll", "timeouts", "resizeObserver", "mutationObserver", "window", "ResizeObserver", "observe", "MutationObserver", "childList", "subtree", "attributes", "attributeFilter", "clearTimeout", "disconnect", "_toolTipGuideMetaData2", "_toolTipGuideMetaData3", "xpath", "value", "result", "document", "evaluate", "XPathResult", "FIRST_ORDERED_NODE_TYPE", "element", "singleNodeValue", "_element$textContent", "rect", "getBoundingClientRect", "info", "tagName", "classes", "className", "position", "x", "y", "textContent", "popup", "querySelector", "HTMLElement", "canvasProperties", "canvas", "children", "place<PERSON><PERSON>nt", "display", "sx", "zoom", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "maxHeight", "options", "suppressScrollY", "suppressScrollX", "wheelPropagation", "swipeEasing", "minScrollbar<PERSON><PERSON>th", "scrollingT<PERSON>eshold", "scrollYMarginOffset", "minHeight", "overflow", "map", "item", "updatedContainers", "draggedItem", "setState", "onMouseEnter", "e", "padding", "borderBottomWidth", "borderBottomColor", "borderBottomStyle", "marginBottom", "draggable", "onDragStart", "onDragEnter", "onDragEnd", "items", "isCloneDisabled", "AddSectionComp", "handleeBlur", "SectionPopOver", "RTEToolbar", "iseditorfocused", "canvasproperties", "length", "DotsStepper", "activeStep", "variant", "margin", "backgroundColor", "BreadCrumpStepper", "_c", "transform", "top", "left", "onClick", "fontSize", "_c2", "_s2", "createTooltipSections", "handleAddSection", "sectionType", "open", "Boolean", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "slotProps", "paper", "gap", "root", "zIndex", "theme", "disableEnforceFocus", "flexDirection", "arrow", "title", "defaultValue", "PopperProps", "alignItems", "cursor", "opacity", "svg", "_c3", "_s3", "_document$getElementB", "_document$getElementB2", "_document$getElementB3", "_document$getElementB4", "_document$getElementB5", "_boxRef$current2", "console", "log", "isRtlDirection", "setIsRtlDirection", "dir", "body", "getAttribute", "toLowerCase", "editorConfig", "direction", "language", "toolbarSticky", "toolbarAdaptive", "autofocus", "buttons", "name", "iconURL", "list", "events", "focus", "afterInit", "editor", "getElementById", "component", "role", "pointerEvents", "min<PERSON><PERSON><PERSON>", "overflowX", "config", "controls", "font", "onBlur", "newContent", "_document$querySelect", "_document$querySelect2", "_document$querySelect3", "_document$querySelect4", "isInsidePopup", "contains", "target", "isInsideJoditPopup", "isInsideWorkplacePopup", "isSelectionMarker", "startsWith", "isLinkPopup", "isInsideToolbarButton", "closest", "isPasteEvent", "ctrl<PERSON>ey", "key", "isInsertButton", "formElement", "onChange", "_c4", "flexGrow", "justifyContent", "background", "nextButton", "backButton", "_c5", "Array", "from", "_", "_c6", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/Tooltips/TooltipBody.tsx"], "sourcesContent": ["import React, { RefObject, useEffect, useRef, useState, useMemo } from \"react\";\r\nimport {\r\n\tBox,\r\n\tClickAwayListener,\r\n\tIconButton,\r\n\tPaper,\r\n\tPopover,\r\n\tTextField,\r\n\tTooltip,\r\n\tTypography,\r\n\tLinearProgress,\r\n\tMobileStepper,\r\n\tBreadcrumbs,\r\n} from \"@mui/material\";\r\nimport { CustomWidthTooltip, EXTENSION_PART, TOOLTIP_HEIGHT, TOOLTIP_MN_WIDTH, TOOLTIP_MX_WIDTH } from \"./Tooltip\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport { Image, TextFormat, Code, VideoLibrary, GifBox, Link } from \"@mui/icons-material\";\r\nimport useDrawerStore, { TSectionType, TooltipState } from \"../../store/drawerStore\";\r\nimport ButtonSection from \"./components/Buttons\";\r\nimport ImageSection from \"./components/ImageSection\";\r\nimport RTEsection from \"./components/RTE/RTESection\";\r\nimport RTE from \"./components/RTE/RTE\";\r\nimport { createPortal } from \"react-dom\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport JoditEditor from \"jodit-react\";\r\nimport AlertPopup from \"../drawer/AlertPopup\";\r\nimport PerfectScrollbar from 'react-perfect-scrollbar';\r\nimport \"react-perfect-scrollbar/dist/css/styles.css\";\r\nimport { useTranslation } from \"react-i18next\";\r\n\r\ntype SectionType = { type: \"image\" | \"button\" | \"video\" | \"gif\" | \"html\" } | { type: \"text\" };\r\n\r\n// Maximum allowed sections of each type\r\nconst MAX_SECTIONS = {\r\n\timage: 3,\r\n\tbutton: 3,\r\n\trte: 3,\r\n};\r\nconst TooltipBody = ({\r\n\tisPopoverOpen,\r\n\tsetIsPopoverOpen,\r\n\tpopupPosition,\r\n\tisUnSavedChanges,\r\n\topenWarning,\r\n\tsetopenWarning,\r\n\thandleLeave,\r\n\tupdatedGuideData,\r\n}: {\r\n\tisPopoverOpen: boolean;\r\n\tsetIsPopoverOpen: (param: boolean) => void;\r\n\tpopupPosition: any;\r\n\tisUnSavedChanges: boolean;\r\n\topenWarning: boolean;\r\n\tsetopenWarning: (params: boolean) => void;\r\n\thandleLeave: () => void;\r\n\t\tupdatedGuideData: any;\r\n}) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\ttoolTipGuideMetaData,\r\n\t\tcurrentStep,\r\n\t\thandleTooltipRTEValue,\r\n\t\thandleRTEDeleteSection,\r\n\t\thandleRTECloneSection,\r\n\t\ttooltip,\r\n\t\ttooltipBackgroundcolor,\r\n\t\topenTooltip,\r\n\t\tselectedOption,\r\n\t\tselectedTemplate,\r\n\t\tselectedTemplateTour,\r\n\t\tsetTooltipPositionByXpath,\r\n\t\twidth,\r\n\t\tborderRadius,\r\n\t\tAnnpadding,\r\n\t\tborderColor,\r\n\t\ttooltipborderradius,\r\n\t\ttooltipbordersize,\r\n\t\ttooltipBordercolor,\r\n\t\ttooltipPosition,\r\n\t\ttooltipWidth,\r\n\t\ttooltippadding,\r\n\t\tAnnborderSize,\r\n\t\tcurrentStepIndex,\r\n\t\tdismissData,\r\n\t\tsteps,\r\n\t\tsetCurrentHoveredElement,\r\n\t\telementClick,\r\n\t\tdismiss,\r\n\t\tsetDismiss,\r\n\t\tprogress,\r\n\t\tsetProgress,\r\n\t\tProgressColor,\r\n\t\tsetProgressColor\r\n\t} = useDrawerStore((state) => state);\r\n\r\n\tconst [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst boxRef = useRef<Map<string, React.RefObject<HTMLDivElement>>>(new Map());\r\n\r\n\tconst [savedRange, setSaveRange] = useState<Range | undefined>(undefined);\r\n\tconst [currentIndex, setCurrentIndex] = useState<number>(0);\r\n\tconst [currentRTEFocusedId, setCurrentRTEFocusedId] = useState<string>(\"\");\r\n\tconst [currentFocusedType, setCurrentFocusedType] = useState<TSectionType>();\r\n\tconst [isEditorFocused, setIsEditorFocused] = useState(false);\r\n\tconst [rteToolbarVisible, setRteToolbarVisible] = useState<{ [key: string]: boolean }>({});\r\n\r\n\t// State to track if scrolling is needed\r\n\tconst [needsScrolling, setNeedsScrolling] = useState(false);\r\n\tconst contentRef = useRef<HTMLDivElement>(null);\r\n\tconst scrollbarRef = useRef<any>(null);\r\n\r\n\t// Section count tracking for limits\r\n\tconst [sectionCounts, setSectionCounts] = useState({\r\n\t\timage: 0,\r\n\t\tbutton: 0,\r\n\t\trte: 0\r\n\t});\r\n\r\n\t// Helper function to check if a section type has reached its limit\r\n\tconst hasReachedLimit = (type: TSectionType): boolean => {\r\n\t\t// Map the section type to the corresponding key in sectionCounts\r\n\t\tconst countType = type === \"rte\" ? \"rte\" : type === \"button\" ? \"button\" : type === \"image\" ? \"image\" : null;\r\n\r\n\t\t// If the type is not supported, return false\r\n\t\tif (countType === null) return false;\r\n\r\n\t\treturn sectionCounts[countType] >= MAX_SECTIONS[countType];\r\n\t};\r\n\tconst handleAddIconClick = (event: React.MouseEvent<HTMLElement>, idx: number) => {\r\n\t\tif (\r\n\t\thasReachedLimit(\"rte\") &&\r\n\t\thasReachedLimit(\"button\") &&\r\n\t\thasReachedLimit(\"image\")\r\n\t\t) {\r\n\t\t\treturn;\r\n\t\t}\r\n\t\tconst currentTarget = event.currentTarget;\r\n\t\tsetTimeout(() => {\r\n\t\t\tsetCurrentIndex(idx);\r\n\t\t\tsetAnchorEl(currentTarget);\r\n\t\t}, 0);\r\n\t};\r\n\r\n\tconst handlePopoverClose = () => {\r\n\t\tsetAnchorEl(null);\r\n\t};\r\n\r\n\tconst handleFocus = (id: string) => {\r\n\t\tsetIsPopoverOpen(true);\r\n\t\tsetCurrentRTEFocusedId(id);\r\n\t};\r\n\r\n\tconst handleBlur = (id: string) => {\r\n\t\tif (boxRef?.current.get(id)?.current?.innerHTML) {\r\n\t\t\thandleTooltipRTEValue(id, boxRef.current.get(id)?.current?.innerHTML.trim() || \"\");\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleDeleteSection = () => {\r\n\t\thandleRTEDeleteSection(currentRTEFocusedId);\r\n\t};\r\n\tconst handleCloneContainer = () => {\r\n\t\thandleRTECloneSection(currentRTEFocusedId);\r\n\t};\r\n\tconst [sections, setSections] = useState<SectionType[]>([{ type: \"image\" }, { type: \"text\" }, { type: \"button\" }]);\r\n\tconst [draggingIndex, setDraggingIndex] = useState<number | null>(null);\r\n\tconst [draggedIndex, setDraggedIndex] = useState<number | null>(null);\r\n\tconst handleDragStart = (index: number) => {\r\n\t\tsetDraggingIndex(index);\r\n\t};\r\n\tconst handleDragEnter = (index: number) => {\r\n\t\tif (draggingIndex !== null && draggingIndex !== index) {\r\n\t\t\tconst reorderedSections = [...sections];\r\n\t\t\tconst [removed] = reorderedSections.splice(draggingIndex, 1);\r\n\t\t\treorderedSections.splice(index, 0, removed);\r\n\t\t\tsetSections(reorderedSections);\r\n\t\t\tsetDraggingIndex(index);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleDragEnd = () => {\r\n\t\tsetDraggingIndex(null);\r\n\t};\r\n\r\n\tconst addToBoxRef = (id: string) => {\r\n\t\tif (!boxRef.current.has(id)) {\r\n\t\t\tconst newRef = React.createRef<HTMLDivElement>();\r\n\t\t\tboxRef.current.set(id, newRef);\r\n\t\t\treturn newRef;\r\n\t\t}\r\n\t\treturn boxRef.current.get(id);\r\n\t};\r\n\r\n\t// Update section counts when the component mounts or when containers change\r\n\tuseEffect(() => {\r\n\t\tif (toolTipGuideMetaData && toolTipGuideMetaData[currentStep - 1]?.containers) {\r\n\t\t\tconst containers = toolTipGuideMetaData[currentStep - 1].containers;\r\n\t\t\tconst counts = {\r\n\t\t\t\timage: 0,\r\n\t\t\t\tbutton: 0,\r\n\t\t\t\trte: 0\r\n\t\t\t};\r\n\r\n\t\t\t// Count each type of section\r\n\t\t\tcontainers.forEach(container => {\r\n\t\t\t\tif (container.type === \"image\") counts.image++;\r\n\t\t\t\telse if (container.type === \"button\") counts.button++;\r\n\t\t\t\telse if (container.type === \"rte\") counts.rte++;\r\n\t\t\t});\r\n\r\n\t\t\tsetSectionCounts(counts);\r\n\t\t}\r\n\t}, [toolTipGuideMetaData, currentStep]);\r\n\r\n\t// Check if content needs scrolling with improved detection\r\n\tuseEffect(() => {\r\n\t\tconst checkScrollNeeded = () => {\r\n\t\t\tif (contentRef.current) {\r\n\t\t\t\t// Force a reflow to get accurate measurements\r\n\t\t\t\tcontentRef.current.style.height = 'auto';\r\n\t\t\t\tconst contentHeight = contentRef.current.scrollHeight;\r\n\t\t\t\tconst containerHeight = 320; // max-height value\r\n\t\t\t\tconst shouldScroll = contentHeight > containerHeight;\r\n\r\n\r\n\t\t\t\tsetNeedsScrolling(shouldScroll);\r\n\r\n\t\t\t\t// Force update scrollbar\r\n\t\t\t\tif (scrollbarRef.current) {\r\n\t\t\t\t\t// Try multiple methods to update the scrollbar\r\n\t\t\t\t\tif (scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// Force re-initialization if needed\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tif (scrollbarRef.current && scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 10);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t\r\n\t\tcheckScrollNeeded();\r\n\r\n\t\t\r\n\t\tconst timeouts = [\r\n\t\t\tsetTimeout(checkScrollNeeded, 50),\r\n\t\t\tsetTimeout(checkScrollNeeded, 100),\r\n\t\t\tsetTimeout(checkScrollNeeded, 200),\r\n\t\t\tsetTimeout(checkScrollNeeded, 500)\r\n\t\t];\r\n\r\n\t\t\r\n\t\tlet resizeObserver: ResizeObserver | null = null;\r\n\t\tlet mutationObserver: MutationObserver | null = null;\r\n\r\n\t\tif (contentRef.current && window.ResizeObserver) {\r\n\t\t\tresizeObserver = new ResizeObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tresizeObserver.observe(contentRef.current);\r\n\t\t}\r\n\r\n\t\t\r\n\t\tif (contentRef.current && window.MutationObserver) {\r\n\t\t\tmutationObserver = new MutationObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tmutationObserver.observe(contentRef.current, {\r\n\t\t\t\tchildList: true,\r\n\t\t\t\tsubtree: true,\r\n\t\t\t\tattributes: true,\r\n\t\t\t\tattributeFilter: ['style', 'class']\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\treturn () => {\r\n\t\t\ttimeouts.forEach(clearTimeout);\r\n\t\t\tif (resizeObserver) {\r\n\t\t\t\tresizeObserver.disconnect();\r\n\t\t\t}\r\n\t\t\tif (mutationObserver) {\r\n\t\t\t\tmutationObserver.disconnect();\r\n\t\t\t}\r\n\t\t};\r\n\t}, [toolTipGuideMetaData, currentStep]);\r\n\r\n\tuseEffect(() => {\r\n\t\tif (openTooltip) {\r\n\t\t\tif (typeof window !== \"undefined\") {\r\n\t\t\t\tconst xpath = toolTipGuideMetaData[currentStep - 1]?.xpath?.value;\r\n\r\n\t\t\t\t// Check if XPath is valid and not empty\r\n\t\t\t\tif (xpath && xpath.trim() !== \"\") {\r\n\t\t\t\t\tconst result = window.document.evaluate(\r\n\t\t\t\t\t\txpath,\r\n\t\t\t\t\t\twindow.document,\r\n\t\t\t\t\t\tnull,\r\n\t\t\t\t\t\tXPathResult.FIRST_ORDERED_NODE_TYPE,\r\n\t\t\t\t\t\tnull\r\n\t\t\t\t\t);\r\n\r\n\t\t\t\t\tconst element = result.singleNodeValue as HTMLElement | null;\r\n\t\t\t\t\tif (element) {\r\n\t\t\t\t\t\tsetCurrentHoveredElement(element);\r\n\t\t\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\t\t\tconst info = {\r\n\t\t\t\t\t\t\telement,\r\n\t\t\t\t\t\t\ttagName: element.tagName,\r\n\t\t\t\t\t\t\tclasses: element.className,\r\n\t\t\t\t\t\t\tid: element.id,\r\n\t\t\t\t\t\t\tposition: {\r\n\t\t\t\t\t\t\t\tx: rect.x,\r\n\t\t\t\t\t\t\t\ty: rect.y,\r\n\t\t\t\t\t\t\t\twidth: rect.width,\r\n\t\t\t\t\t\t\t\theight: rect.height,\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\ttextContent: element.textContent?.trim() || \"\",\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\tsetTooltipPositionByXpath(info);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tlet popup = document.querySelector(\".MuiTooltip-popperInteractive\");\r\n\t\t\t\tif (popup instanceof HTMLElement && popupPosition) {\r\n\t\t\t\t\t//popup.style.top = `${popupPosition.top || \"20px\"}`;\r\n\t\t\t\t\t//popup.style.left = `${popupPosition.left || \"10px\"}`; // Example for setting the left position\r\n\t\t\t\t}\r\n\t\t\t}, 10);\r\n\t\t}\r\n\t}, [openTooltip]);\r\n\r\n\tconst canvasProperties = toolTipGuideMetaData[currentStep - 1]?.canvas;\r\n\r\n\treturn (\r\n\t\t<div>\r\n\t\t<div style={{ placeContent: \"end\", display: \"flex\" }}>\r\n\t\t\t\t{!isPopoverOpen && dismiss && (\r\n\t\t\t\t\t<IconButton className=\"qadpt-dismiss\"\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon sx={{ zoom: \"1\", color: \"#000\" }} />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t)}\r\n\t\t\t</div>\r\n\t\t\t\r\n\t\t\t<PerfectScrollbar\r\n\t\t\t\tkey={`scrollbar-${needsScrolling}`}\r\n\t\t\t\tref={scrollbarRef}\r\n\t\t\t\tstyle={{ maxHeight: \"320px\" }}\r\n\t\t\t\toptions={{\r\n\t\t\t\t\tsuppressScrollY: !needsScrolling,\r\n\t\t\t\t\tsuppressScrollX: true,\r\n\t\t\t\t\twheelPropagation: false,\r\n\t\t\t\t\tswipeEasing: true,\r\n\t\t\t\t\tminScrollbarLength: 20,\r\n\t\t\t\t\tscrollingThreshold: 1000,\r\n\t\t\t\t\tscrollYMarginOffset: 0\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t<div\r\n\t\t\tref={contentRef}\r\n\t\t\tstyle={{\r\n\t\t\t\t// backgroundColor: canvasProperties?.backgroundColor || \"white\",\r\n\t\t\t\t// padding: canvasProperties?.padding || \"4px\",\r\n\t\t\t\t// width: `${toolTipGuideMetaData[currentStep - 1]?.canvas.width} !important` || \"500px !important\",\r\n\t\t\t\t// borderRadius: canvasProperties?.borderRadius || \"8px\",\r\n\t\t\t\t// minWidth: TOOLTIP_MN_WIDTH,\r\n\t\t\t\t// maxWidth: TOOLTIP_MX_WIDTH,\r\n\t\t\t\tminHeight: 60,\r\n\t\t\t\t// maxHeight: 320,\r\n\t\t\t\t// height: selectedTemplate === \"Tooltip\" ? \"auto\" : \"350px\",\r\n\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\tposition: \"relative\",\r\n\t\t\t}}\r\n\t\t>\r\n\t\t\t\r\n\t\t\t{toolTipGuideMetaData[currentStep - 1]?.containers.map((item, index) => {\r\n\t\t\t\tconst id = `${item.type}|${item.id}`;\r\n\t\t\t\tconst handleDragStart = (index: number) => {\r\n\t\t\t\t\tif (draggedIndex === index) return; // Prevent redundant drag starts\r\n\t\t\t\t\tsetDraggedIndex(index);\r\n\t\t\t\t};\r\n\r\n\t\t\t\tconst handleDragEnter = (index: number) => {\r\n\t\t\t\t\tif (draggedIndex !== null && draggedIndex !== index) {\r\n\t\t\t\t\t\tconst updatedContainers = [...toolTipGuideMetaData[currentStep - 1].containers];\r\n\t\t\t\t\t\tconst [draggedItem] = updatedContainers.splice(draggedIndex, 1);\r\n\t\t\t\t\t\tupdatedContainers.splice(index, 0, draggedItem);\r\n\r\n\t\t\t\t\t\t// Update state only if the order has changed\r\n\t\t\t\t\t\tuseDrawerStore.setState({\r\n\t\t\t\t\t\t\ttoolTipGuideMetaData: [\r\n\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t...toolTipGuideMetaData[currentStep - 1],\r\n\t\t\t\t\t\t\t\t\tcontainers: updatedContainers,\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t],\r\n\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\tsetDraggedIndex(index);\r\n\t\t\t\t\t}\r\n\t\t\t\t};\r\n\r\n\t\t\t\tconst handleDragEnd = () => {\r\n\t\t\t\t\tsetDraggedIndex(null);\r\n\t\t\t\t};\r\n\t\t\t\treturn (\r\n\t\t\t\t\t<>\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tonMouseEnter={(e) => {\r\n\t\t\t\t\t\t\t\tsetCurrentFocusedType(e.currentTarget.id as TSectionType);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tposition: \"relative\",\r\n\t\t\t\t\t\t\t\tpadding: \"7px 0\",\r\n\t\t\t\t\t\t\t\tborderBottomWidth: currentFocusedType === id ? \"1px\" : \"0px\",\r\n\t\t\t\t\t\t\t\tborderBottomColor: currentFocusedType === id ? \"var(--primarycolor)\" : \"transparent\",\r\n\t\t\t\t\t\t\t\tborderBottomStyle: currentFocusedType === id ? \"dotted\" : \"none\",\r\n\t\t\t\t\t\t\t\tmarginBottom:\"10px\"\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tid={id}\r\n\t\t\t\t\t\t\tdraggable={item.type !== \"rte\"}\r\n\t\t\t\t\t\t\tonDragStart={() => handleDragStart(index)}\r\n\t\t\t\t\t\t\tonDragEnter={() => handleDragEnter(index)}\r\n\t\t\t\t\t\t\tonDragEnd={handleDragEnd}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{item.type === \"button\" ? (\r\n\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t<ButtonSection\r\n\t\t\t\t\t\t\t\t\t\titems={item}\r\n\t\t\t\t\t\t\t\t\t\tupdatedGuideData={updatedGuideData}\r\n\t\t\t\t\t\t\t\t\t\tisCloneDisabled={hasReachedLimit(\"button\")}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t{currentFocusedType === id && (\r\n\t\t\t\t\t\t\t\t\t\t<AddSectionComp handleAddIconClick={(e) => handleAddIconClick(e, index)} />\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t) : item.type === \"image\" ? (\r\n\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t<ImageSection\r\n\t\t\t\t\t\t\t\t\t\titems={item}\r\n\t\t\t\t\t\t\t\t\t\tisCloneDisabled={hasReachedLimit(\"image\")}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t{currentFocusedType === id && (\r\n\t\t\t\t\t\t\t\t\t\t<AddSectionComp handleAddIconClick={(e) => handleAddIconClick(e, index)} />\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t) : item.type === \"rte\" ? (\r\n\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t<RTEsection\r\n\t\t\t\t\t\t\t\t\t\titems={item}\r\n\t\t\t\t\t\t\t\t\t\t//@ts-ignore\r\n\t\t\t\t\t\t\t\t\t\tboxRef={addToBoxRef(item.id)}\r\n\t\t\t\t\t\t\t\t\t\thandleFocus={handleFocus}\r\n\t\t\t\t\t\t\t\t\t\thandleeBlur={handleBlur}\r\n\t\t\t\t\t\t\t\t\t\tisPopoverOpen={isPopoverOpen}\r\n\t\t\t\t\t\t\t\t\t\tsetIsPopoverOpen={setIsPopoverOpen}\r\n\t\t\t\t\t\t\t\t\t\tcurrentRTEFocusedId={currentRTEFocusedId}\r\n\t\t\t\t\t\t\t\t\t\tisCloneDisabled={hasReachedLimit(\"rte\")}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t{currentFocusedType === id && (\r\n\t\t\t\t\t\t\t\t\t\t<AddSectionComp handleAddIconClick={(e) => handleAddIconClick(e, index)} />\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t) : null}\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</>\r\n\t\t\t\t);\r\n\t\t\t})}\r\n\r\n\t\t\t\r\n\r\n\t\t\t{/* {anchorEl ? ( */}\r\n\t\t\t<SectionPopOver\r\n\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\thandlePopoverClose={handlePopoverClose}\r\n\t\t\t\tcurrentIndex={currentIndex}\r\n\t\t\t\thasReachedLimit={hasReachedLimit}\r\n\t\t\t/>\r\n\t\t\t{/* ) : null} */}\r\n\t\t\t{isUnSavedChanges && openWarning && (\r\n\t\t\t\t<AlertPopup\r\n\t\t\t\t\topenWarning={openWarning}\r\n\t\t\t\t\tsetopenWarning={setopenWarning}\r\n\t\t\t\t\thandleLeave={handleLeave}\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n\t\t\t{isPopoverOpen && currentRTEFocusedId && boxRef?.current?.get(currentRTEFocusedId) && (\r\n\t\t\t\t<RTEToolbar\r\n\t\t\t\t\tisPopoverOpen={isPopoverOpen}\r\n\t\t\t\t\tsetIsPopoverOpen={setIsPopoverOpen}\r\n\t\t\t\t\tboxRef={boxRef.current.get(currentRTEFocusedId)}\r\n\t\t\t\t\tcurrentRTEFocusedId={currentRTEFocusedId}\r\n\t\t\t\t\thandleDeleteSection={handleDeleteSection}\r\n\t\t\t\t\thandleCloneContainer={handleCloneContainer}\r\n\t\t\t\t\tsavedRange={savedRange}\r\n\t\t\t\t\tsetSaveRange={setSaveRange}\r\n\t\t\t\t\ttooltip={tooltip}\r\n\t\t\t\t\tpopupPosition={popupPosition}\r\n\t\t\t\t\thandleTooltipRTEValue={handleTooltipRTEValue}\r\n\t\t\t\t\tiseditorfocused={isEditorFocused}\r\n\t\t\t\t\tsetIsEditorFocused={setIsEditorFocused}\r\n\t\t\t\t\tcanvasproperties={canvasProperties}\r\n\t\t\t\t\t\t\tisCloneDisabled={hasReachedLimit(\"rte\")}\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n\t\t\t\t</div>\r\n\t\t\t\t</PerfectScrollbar>\r\n\t\t{progress && toolTipGuideMetaData.length>1 &&\r\n\t\t\t\t(selectedTemplate === \"Tooltip\"  || selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\") && \r\n\t\t\t\t(selectedOption === 1 || selectedOption === \"\" ? (\r\n\t\t\t\t\t<DotsStepper\r\n\t\t\t\t\t\tactiveStep={currentStep}\r\n\t\t\t\t\t\tsteps={steps.length}\r\n\t\t\t\t\t\tProgressColor = {ProgressColor}\r\n\t\t\t\t\t/>\r\n\t\t\t\t) : selectedOption === 2 ? (\r\n\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<LinearProgress\r\n\t\t\t\t\t\t\tvariant=\"determinate\"\r\n\t\t\t\t\t\t\t//sx={{paddingTop:\"15px\",padding:\"8px\"}}\r\n\t\t\t\t\t\t\tvalue={(currentStep / steps.length) * 100}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\theight: \"6px\",\r\n\t\t\t\t\t\t\t\tborderRadius: \"20px\",\r\n\t\t\t\t\t\t\t\tmargin: \"6px 10px\",\r\n\t\t\t\t\t\t\t\t'& .MuiLinearProgress-bar': {\r\n                                backgroundColor: ProgressColor, // progress bar color\r\n                              },}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t) \r\n\t\t\t\t: selectedOption === 3 ? (\r\n\t\t\t\t\t<div style={{padding:\"8px\"}}>\r\n\t\t\t\t\t\t<BreadCrumpStepper\r\n\t\t\t\t\t\tactiveStep={currentStep}\r\n\t\t\t\t\t\tsteps={steps.length}\r\n\t\t\t\t\t\tProgressColor={ProgressColor}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t) : selectedOption === 4 ? (\r\n\t\t\t\t\t<Breadcrumbs\r\n\t\t\t\t\t\taria-label=\"breadcrumb\"\r\n\t\t\t\t\t\tsx={{ padding: \"8px\" }}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<Typography sx={{color : ProgressColor}}>\r\n\t\t\t\t\t\t\tStep {currentStep} of {steps.length}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t</Breadcrumbs>\r\n\t\t\t\t) : null)}\r\n\t\t</div>\r\n\t);\r\n};\r\nexport default TooltipBody;\r\n\r\nconst AddSectionComp = ({\r\n\thandleAddIconClick,\r\n}: {\r\n\thandleAddIconClick: (event: React.MouseEvent<HTMLElement>) => void;\r\n}) => {\r\n\treturn (\r\n\t\t<Box sx={{ position: \"absolute\", transform: \"translate(-50%,-50%)\", top: \"99%\", left: \"50%\" }}>\r\n\t\t\t<IconButton\r\n\t\t\t\tonClick={handleAddIconClick}\r\n\t\t\t\tsx={{\r\n\t\t\t\t\tbackgroundColor: \"#5F9EA0\",\r\n\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\tbackgroundColor: \"#70afaf\",\r\n\t\t\t\t\t},\r\n\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\tpadding: \"5px !important\",\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<AddIcon\r\n\t\t\t\t\tfontSize=\"small\"\r\n\t\t\t\t\tsx={{ color: \"#fff\" }}\r\n\t\t\t\t/>\r\n\t\t\t</IconButton>\r\n\t\t</Box>\r\n\t);\r\n};\r\n\r\nconst SectionPopOver = ({\r\n\tanchorEl,\r\n\thandlePopoverClose,\r\n\tcurrentIndex,\r\n\thasReachedLimit\r\n}: {\r\n\tanchorEl: HTMLElement | null;\r\n\thandlePopoverClose: () => void;\r\n\tcurrentIndex: number;\r\n\thasReachedLimit: (type: TSectionType) => boolean;\r\n}) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst { createTooltipSections, selectedOption, currentStep, steps, currentStepIndex } = useDrawerStore(\r\n\t\t(state: any) => state\r\n\t);\r\n\tconst handleAddSection = (sectionType: TSectionType) => {\r\n\t\t// Don't add if limit is reached\r\n\t\tif (hasReachedLimit(sectionType)) {\r\n\t\t\t\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tcreateTooltipSections(sectionType, currentIndex);\r\n\t\thandlePopoverClose();\r\n\t};\r\n\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{anchorEl && (\r\n\t\t\t\t<Popover\r\n\t\t\t\t\topen={Boolean(anchorEl)}\r\n\t\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\t\tonClose={handlePopoverClose}\r\n\t\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tid=\"tooltip-section-popover\"\r\n\t\t\t\t\tslotProps={{\r\n\t\t\t\t\t\tpaper: {\r\n\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\tpadding: \"12px\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\tgap: \"16px\",\r\n\t\t\t\t\t\t\twidth: \"auto\",\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\troot: {\r\n\t\t\t\t\t\t\t// instead of writing sx on popover write here it also target to root and more clear\r\n\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\tzIndex: (theme) => theme.zIndex.tooltip + 1000,\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tdisableEnforceFocus={true}\r\n\t\t\t\t>\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\tflexDirection=\"row\"\r\n\t\t\t\t\t\tgap=\"16px\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<Tooltip arrow\r\n\t\t\t\t\t\t\ttitle={hasReachedLimit(\"rte\") ? translate(\"Maximum limit of 3 Rich Text sections reached\", { defaultValue: \"Maximum limit of 3 Rich Text sections reached\" }) : \"\"}\r\n\t\t\t\t\t\t\tPopperProps={{\r\n\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tcursor: hasReachedLimit(\"rte\") ? \"not-allowed\" : \"pointer\",\r\n\t\t\t\t\t\t\t\t\topacity: hasReachedLimit(\"rte\") ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\tsvg: {\r\n\t\t\t\t\t\t\t\t\t\tfontSize: \"24px !important\"\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tonClick={() => !hasReachedLimit(\"rte\") && handleAddSection(\"rte\")}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<TextFormat />\r\n\t\t\t\t\t\t\t\t<Typography sx={{ fontSize: \"11px !important\" }}>{translate(\"Rich Text\", { defaultValue: \"Rich Text\" })}</Typography>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</Tooltip>\r\n\r\n\t\t\t\t\t\t<Tooltip arrow\r\n\t\t\t\t\t\t\ttitle={hasReachedLimit(\"button\") ? translate(\"Maximum limit of 3 Button sections reached\", { defaultValue: \"Maximum limit of 3 Button sections reached\" }) : \"\"}\r\n\t\t\t\t\t\t\tPopperProps={{\r\n\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tcursor: hasReachedLimit(\"button\") ? \"not-allowed\" : \"pointer\",\r\n\t\t\t\t\t\t\t\t\topacity: hasReachedLimit(\"button\") ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\tsvg: {\r\n\t\t\t\t\t\t\t\t\t\tfontSize: \"24px !important\"\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tonClick={() => !hasReachedLimit(\"button\") && handleAddSection(\"button\")}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Link />\r\n\t\t\t\t\t\t\t\t<Typography sx={{ fontSize: \"11px !important\" }}>{translate(\"Button\", { defaultValue: \"Button\" })}</Typography>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</Tooltip>\r\n\r\n\t\t\t\t\t\t<Tooltip arrow\r\n\t\t\t\t\t\t\ttitle={hasReachedLimit(\"image\") ? translate(\"Maximum limit of 3 Image sections reached\", { defaultValue: \"Maximum limit of 3 Image sections reached\" }) : \"\"}\r\n\t\t\t\t\t\t\tPopperProps={{\r\n\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tcursor: hasReachedLimit(\"image\") ? \"not-allowed\" : \"pointer\",\r\n\t\t\t\t\t\t\t\t\topacity: hasReachedLimit(\"image\") ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\tsvg: {\r\n\t\t\t\t\t\t\t\t\t\tfontSize: \"24px !important\"\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tonClick={() => !hasReachedLimit(\"image\") && handleAddSection(\"image\")}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Image />\r\n\t\t\t\t\t\t\t\t<Typography sx={{ fontSize: \"11px !important\" }}>{translate(\"Image\", { defaultValue: \"Image\" })}</Typography>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</Tooltip>\r\n\r\n\t\t\t\t\t\t{/* <Tooltip\r\n\t\t\t\t\ttitle=\"Coming Soon\"\r\n\t\t\t\t\tPopperProps={{\r\n\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<span>\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\tsx={{ cursor: \"pointer\", opacity: 0.5 }}\r\n\t\t\t\t\t\t\t// onClick={() => handleAddSection(\"video\")}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<VideoLibrary />\r\n\t\t\t\t\t\t\t<Typography variant=\"caption\">Video</Typography>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</span>\r\n\t\t\t\t</Tooltip>\r\n\t\t\t\t<Box\r\n\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\tsx={{ cursor: \"pointer\", opacity: 0.5 }}\r\n\t\t\t\t\t// onClick={() => handleAddSection(\"gif\")}\r\n\t\t\t\t>\r\n\t\t\t\t\t<GifBox />\r\n\t\t\t\t\t<Typography variant=\"caption\">Gif</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t\t<Tooltip\r\n\t\t\t\t\ttitle=\"Coming Soon\"\r\n\t\t\t\t\tPopperProps={{\r\n\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<span>\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\tsx={{ cursor: \"pointer\", opacity: 0.5 }}\r\n\t\t\t\t\t\t\t// onClick={() => handleAddSection(\"html\")}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Code />\r\n\t\t\t\t\t\t\t<Typography variant=\"caption\">HTML</Typography>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</span>\r\n\t\t\t\t</Tooltip> */}\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t</Popover>\r\n\t\t\t)}\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nconst RTEToolbar = ({\r\n\tisPopoverOpen,\r\n\tcurrentRTEFocusedId,\r\n\ttooltip,\r\n\tsetIsPopoverOpen,\r\n\tboxRef,\r\n\tsavedRange,\r\n\thandleDeleteSection,\r\n\thandleCloneContainer,\r\n\tsetSaveRange,\r\n\tpopupPosition,\r\n\thandleTooltipRTEValue,\r\n\tiseditorfocused,\r\n\tsetIsEditorFocused,\r\n\tcanvasproperties,\r\n\tisCloneDisabled,\r\n}: {\r\n\tisPopoverOpen: boolean;\r\n\tcurrentRTEFocusedId: string;\r\n\ttooltip: TooltipState;\r\n\tsetIsPopoverOpen: (params: boolean) => void;\r\n\tboxRef: React.RefObject<HTMLDivElement> | undefined;\r\n\tsavedRange: Range | undefined;\r\n\thandleDeleteSection: () => void;\r\n\thandleCloneContainer: () => void;\r\n\tsetSaveRange: (params: Range) => void;\r\n\tpopupPosition: any;\r\n\thandleTooltipRTEValue: (id: string, value: string) => void;\r\n\tiseditorfocused: boolean;\r\n\tsetIsEditorFocused: (params: boolean) => void;\r\n\tcanvasproperties: any;\r\n\t\tisCloneDisabled?: boolean;\r\n\t// open: boolean;\r\n}) => {\r\n\t// const handleRTEPopoverClose = (event: MouseEvent | TouchEvent) => {\r\n\t// \tconst target = event.target as HTMLElement;\r\n\r\n\t// \t// Check if the target is within specific editable or toolbar elements\r\n\t// \tconst isElement =\r\n\t// \t\ttarget.closest(\"[contenteditable]\") ||\r\n\t// \t\ttarget.closest(\"#rte-toolbar-paper\") ||\r\n\t// \t\ttarget.closest(\"#rte-alignment-menu\") ||\r\n\t// \t\ttarget.closest(\"#rte-alignment-menu-items\") ||\r\n\t// \t\ttarget.closest(target.id.startsWith(\"#rt-editor\") ? `${target.id}` : \"nope\");\r\n\t// \tif (target && isElement) {\r\n\t// \t\treturn;\r\n\t// \t}\r\n\r\n\t// \t// Check if the target is within Jodit Editor container\r\n\t// \t//const isJoditContainer = target.classList.contains(\"backdrop\");\r\n\t// \t//const isClickedoutside =  target.classList.contains(\"quickAdopt-selection\");\r\n\r\n\t// \t// Handle dynamic ID checks safely\r\n\t// \t// const isRTEditor = target.id.startsWith(\"rt-editor\") && target.closest(`#${target.id}`);\r\n\r\n\t// \t// if (isJoditContainer || isElement || isRTEditor) {\r\n\t// \t// \treturn;\r\n\t// \t// }\r\n\r\n\t// \t// Delay closing the popover slightly\r\n\r\n\t// \t\tsetIsPopoverOpen(false);\r\n\r\n\t// };\r\n\r\n\t// const handleBlur = () => {\r\n\t// \tsetIsEditorFocused(false);\r\n\t//     console.log(\"Editor lost focus\");\r\n\t// };\r\n\r\n\tconst handleFocus = () => {\r\n\t\tsetIsEditorFocused(true);\r\n\t\tconsole.log(\"Editor is focused\");\r\n\t};\r\n\tconst [isRtlDirection, setIsRtlDirection] = useState<boolean>(false);\r\n\tuseEffect(() => {\r\n    const dir = document.body.getAttribute(\"dir\") || \"ltr\";\r\n    setIsRtlDirection(dir.toLowerCase() === \"rtl\");\r\n}, []);\r\n\tconst editorConfig = useMemo(\r\n\t\t() => ({\r\n\t\t\tdirection: isRtlDirection ? 'rtl' as const : 'ltr' as const,\r\n            \r\n\t\t\t// Jodit uses 'direction' not just 'rtl'\r\n\t\t\t\t\tlanguage:  'en', // Optional: change language as well\r\n\t\t\ttoolbarSticky: false,\r\n\t\t\ttoolbarAdaptive: false,\r\n\t\t\tautofocus: true, // Enable auto-focus for immediate interaction\r\n\t\t\tbuttons: [\r\n\t\t\t\t\"bold\",\r\n\t\t\t\t\"italic\",\r\n\t\t\t\t\"underline\",\r\n\t\t\t\t\"brush\",\r\n\t\t\t\t\"font\",\r\n\t\t\t\t\"fontsize\",\r\n\t\t\t\t\"link\",\r\n\t\t\t\t{\r\n\t\t\t\t\tname: \"more\",\r\n\t\t\t\t\ticonURL: \"https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg\",\r\n\t\t\t\t\tlist: [\r\n\t\t\t\t\t\t\"source\",\r\n\t\t\t\t\t\t\"strikethrough\",\r\n\t\t\t\t\t\t\"ul\",\r\n\t\t\t\t\t\t\"ol\",\r\n\t\t\t\t\t\t\"image\",\r\n\t\t\t\t\t\t\"video\",\r\n\t\t\t\t\t\t\"table\",\r\n\t\t\t\t\t\t\"align\",\r\n\t\t\t\t\t\t\"undo\",\r\n\t\t\t\t\t\t\"redo\",\r\n\t\t\t\t\t\t\"|\",\r\n\t\t\t\t\t\t\"hr\",\r\n\t\t\t\t\t\t\"eraser\",\r\n\t\t\t\t\t\t\"copyformat\",\r\n\t\t\t\t\t\t\"symbol\",\r\n\t\t\t\t\t\t\"fullsize\",\r\n\t\t\t\t\t\t\"print\",\r\n\t\t\t\t\t\t\"superscript\",\r\n\t\t\t\t\t\t\"subscript\",\r\n\t\t\t\t\t\t\"|\",\r\n\t\t\t\t\t\t\"outdent\",\r\n\t\t\t\t\t\t\"indent\",\r\n\t\t\t\t\t\t\"paragraph\",\r\n\t\t\t\t\t],\r\n\t\t\t\t},\r\n\t\t\t],\r\n\t\t\tevents: {\r\n\t\t\t\tfocus: handleFocus,\r\n\t\t\t\t//blur: handleBlur\r\n\t\t\t\tafterInit: (editor: any) => {\r\n\t\t\t\t\t// Ensure the editor is focused immediately after initialization\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\teditor.focus();\r\n\t\t\t\t\t}, 0);\r\n\t\t\t\t},\r\n\t\t\t},\r\n\t\t\tmaxHeight: \"calc(100% - 90px)\",\r\n\t\t}),\r\n\t\t[isRtlDirection]\r\n\t);\r\n\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{isPopoverOpen &&\r\n\t\t\t\tcreatePortal(\r\n\t\t\t\t\t// <ClickAwayListener onClickAway={(event) => {\r\n\t\t\t\t\t// \tconst target = event.target as HTMLElement;\r\n\t\t\t\t\t// \t// const isJoditContainer = target.classList.contains(\"backdrop\");\r\n\t\t\t\t\t// \t// const isClickedoutside = target.classList.contains(\"quickAdopt-selection\");\r\n\t\t\t\t\t// \tconst isInsidePopup = document.querySelector(\".jodit-popup\")?.contains(event.target as Node);\r\n\t\t\t\t\t// \tconst isInsideJoditPopup = document.querySelector(\".jodit-wysiwyg\")?.contains(event.target as Node);\r\n\t\t\t\t\t// \tconst isInsideWorkplacePopup = isInsideJoditPopup || document.querySelector(\".jodit-dialog__panel\")?.contains(event.target as Node);\r\n\t\t\t\t\t// \tconst isSelectionMarker = (event.target as HTMLElement).id.startsWith(\"jodit-selection_marker_\");\r\n\t\t\t\t\t// \tconst isLinkPopup = document.querySelector(\".jodit-ui-input__input\")?.contains(event.target as Node);\r\n\t\t\t\t\t// \t// Check if the target is inside a button, and if it is, check if it's inside the toolbar button container\r\n\t\t\t\t\t// \tconst isInsideToolbarButton = (event.target as HTMLElement).closest(\".jodit-toolbar-button__button\") !== null;\r\n\t\t\t\t\t// \t// Check if the target is the specific \"Insert\" button (or similar button you want)\r\n\t\t\t\t\t// \tconst isInsertButton = (event.target as HTMLElement).closest(\"button[aria-pressed='false']\") !== null;\r\n\t\t\t\t\t// \tif (!isInsidePopup && !isInsideWorkplacePopup && !isSelectionMarker && !isLinkPopup && !isInsideToolbarButton && !isInsertButton) {\r\n\t\t\t\t\t// \t\thandleRTEPopoverClose(event);\r\n\t\t\t\t\t// \t}\r\n\t\t\t\t\t// }}>\r\n\t\t\t\t\t<Paper\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\t\t\tzIndex: 99999,\r\n\t\t\t\t\t\t\tleft: (document.getElementById(\"Tooltip-unique\")?.getBoundingClientRect()?.x ?? 150) + 3,\r\n\t\t\t\t\t\t\t// @ts-ignore\r\n\t\t\t\t\t\t\ttop: document.getElementById(\"Tooltip-unique\")?.getBoundingClientRect()?.y - 50 || 80,\r\n\t\t\t\t\t\t\twidth: canvasproperties?.width || \"300px\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\tid=\"rte-toolbar-paper\"\r\n\t\t\t\t\t\trole=\"presentation\"\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\"& .jodit-status-bar-link\": {\r\n\t\t\t\t\t\t\t\tdisplay: \"none !important\",\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\"& .jodit-ui-input__wrapper\": {\r\n\t\t\t\t\t\t\t\tpointerEvents: \"auto\",\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\"& .jodit-toolbar-button button\": {\r\n\t\t\t\t\t\t\t\tminWidth: \"8px !important\",\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\"& .jodit-workplace\": {\r\n\t\t\t\t\t\t\t\tmaxHeight: \"calc(100vh - 290px) !important\",\r\n\t\t\t\t\t\t\t\toverflowX: \"auto !important\",\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<JoditEditor\r\n\t\t\t\t\t\t\tvalue={boxRef?.current?.innerHTML || \"\"}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-jodit\"\r\n\t\t\t\t\t\t\tconfig={{\r\n\t\t\t\t\t\t\t\t...editorConfig,\r\n\t\t\t\t\t\t\t\tcontrols: {\r\n\t\t\t\t\t\t\t\t\tfont: {\r\n\t\t\t\t\t\t\t\t\t\tlist: {\r\n\t\t\t\t\t\t\t\t\t\t\t\"Poppins, sans-serif\": \"Poppins\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"Roboto, sans-serif\": \"Roboto\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"Comic Sans MS, sans-serif\": \"Comic Sans MS\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"Open Sans, sans-serif\": \"Open Sans\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"Calibri, sans-serif\": \"Calibri\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"Century Gothic, sans-serif\": \"Century Gothic\",\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tonBlur={(newContent, event) => {\r\n\t\t\t\t\t\t\t\tconst isInsidePopup = document.querySelector(\".jodit-popup\")?.contains(event.target as Node);\r\n\t\t\t\t\t\t\t\tconst isInsideJoditPopup = document.querySelector(\".jodit-wysiwyg\")?.contains(event.target as Node);\r\n\t\t\t\t\t\t\t\tconst isInsideWorkplacePopup = document\r\n\t\t\t\t\t\t\t\t\t.querySelector(\".jodit-dialog__panel\")\r\n\t\t\t\t\t\t\t\t\t?.contains(event.target as Node);\r\n\t\t\t\t\t\t\t\tconst isSelectionMarker = (event.target as HTMLElement).id.startsWith(\"jodit-selection_marker_\");\r\n\t\t\t\t\t\t\t\tconst isLinkPopup = document.querySelector(\".jodit-ui-input__input\")?.contains(event.target as Node);\r\n\t\t\t\t\t\t\t\t// Check if the target is inside a button, and if it is, check if it's inside the toolbar button container\r\n\t\t\t\t\t\t\t\tconst isInsideToolbarButton =\r\n\t\t\t\t\t\t\t\t\t(event.target as HTMLElement).closest(\".jodit-toolbar-button__button\") !== null;\r\n\t\t\t\t\t\t\t\tconst isPasteEvent =\r\n\t\t\t\t\t\t\t\t\tevent.type === \"paste\" ||\r\n\t\t\t\t\t\t\t\t\t(event.type === \"keydown\" &&\r\n\t\t\t\t\t\t\t\t\t\t(event as unknown as KeyboardEvent).ctrlKey &&\r\n\t\t\t\t\t\t\t\t\t\t(event as unknown as KeyboardEvent).key === \"v\");\r\n\t\t\t\t\t\t\t\t// Check if the target is the specific \"Insert\" button (or similar button you want)\r\n\t\t\t\t\t\t\t\tconst isInsertButton = (event.target as HTMLElement).closest(\"button[aria-pressed='false']\") !== null;\r\n\t\t\t\t\t\t\t\tconst formElement = document.querySelector(\".jodit-ui-form\");\r\n\r\n\t\t\t\t\t\t\t\tif (\r\n\t\t\t\t\t\t\t\t\tboxRef?.current &&\r\n\t\t\t\t\t\t\t\t\t!isInsidePopup &&\r\n\t\t\t\t\t\t\t\t\t!isSelectionMarker &&\r\n\t\t\t\t\t\t\t\t\t!isLinkPopup &&\r\n\t\t\t\t\t\t\t\t\t!isInsideToolbarButton &&\r\n\t\t\t\t\t\t\t\t\t!isInsertButton &&\r\n\t\t\t\t\t\t\t\t\t!formElement &&\r\n\t\t\t\t\t\t\t\t\tisInsideWorkplacePopup === undefined\r\n\t\t\t\t\t\t\t\t) {\r\n\t\t\t\t\t\t\t\t\tboxRef.current.innerHTML = newContent;\r\n\t\t\t\t\t\t\t\t\thandleTooltipRTEValue(currentRTEFocusedId, newContent.trim());\r\n\t\t\t\t\t\t\t\t\tsetIsPopoverOpen(false);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tonChange={(newContent) => {\r\n\t\t\t\t\t\t\t\tif (boxRef?.current) {\r\n\t\t\t\t\t\t\t\t\tboxRef.current.innerHTML = newContent;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Paper>,\r\n\t\t\t\t\t// </ClickAwayListener >\r\n\t\t\t\t\tdocument.body,\r\n\t\t\t\t\t\"rte-toolbar-portal\"\r\n\t\t\t\t)}\r\n\t\t</>\r\n\t);\r\n};\r\nconst DotsStepper = ({ steps, activeStep, ProgressColor }: { steps: number; activeStep: number; ProgressColor:string }) => {\r\n\treturn (\r\n\t\t<MobileStepper\r\n\t\t\tvariant=\"dots\"\r\n\t\t\tsteps={steps}\r\n\t\t\tposition=\"static\"\r\n\t\t\tactiveStep={activeStep - 1}\r\n\t\t\tsx={{ flexGrow: 1, display: \"flex\", justifyContent: \"center\" ,background:\"inherit\", \"& .MuiMobileStepper-dotActive\": {\r\n\t\t\t\tbackgroundColor: ProgressColor, // active dot color\r\n\t\t\t  }}}\r\n\t\t\tnextButton={<></>}\r\n\t\t\tbackButton={<></>}\r\n\t\t/>\r\n\t);\r\n};\r\nconst BreadCrumpStepper = ({ steps, activeStep,ProgressColor }: { steps: number; activeStep: number; ProgressColor:any }) => {\r\n\treturn (\r\n\t\t<Box sx={{\r\n\t\t\tdisplay: 'flex',\r\n\t\t\tjustifyContent: 'center',\r\n\t\t\tgap: \"4px\", // Adjust space between steps\r\n\t\t\t\r\n\t\t  }}>\r\n\t\t  {/* Custom Step Indicators */}\r\n\t\t\r\n\t\t\t{Array.from({ length: steps }).map((_, index) => (\r\n\t\t\t  <div\r\n\t\t\t\tkey={index}\r\n\t\t\t\tstyle={{\r\n\t\t\t\t  width: '14px',\r\n\t\t\t\t  height: '4px',\r\n\t\t\t\t  backgroundColor: index === activeStep - 1 ? ProgressColor : '#e0e0e0', // Active color and inactive color\r\n\t\t\t\t  borderRadius: '100px',\r\n\t\t\t\t}}\r\n\t\t\t  />\r\n\t\t\t))}\r\n\t\t\r\n\t\t</Box>\r\n\t  );\r\n};\r\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAeC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,QAAQ,OAAO;AAC9E,SACCC,GAAG,EAEHC,UAAU,EACVC,KAAK,EACLC,OAAO,EAEPC,OAAO,EACPC,UAAU,EACVC,cAAc,EACdC,aAAa,EACbC,WAAW,QACL,eAAe;AAEtB,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,SAASC,KAAK,EAAEC,UAAU,EAA8BC,IAAI,QAAQ,qBAAqB;AACzF,OAAOC,cAAc,MAAsC,yBAAyB;AACpF,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,UAAU,MAAM,6BAA6B;AAEpD,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,WAAW,MAAM,aAAa;AACrC,OAAOC,UAAU,MAAM,sBAAsB;AAC7C,OAAOC,gBAAgB,MAAM,yBAAyB;AACtD,OAAO,6CAA6C;AACpD,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAI/C;AACA,MAAMC,YAAY,GAAG;EACpBC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,GAAG,EAAE;AACN,CAAC;AACD,MAAMC,WAAW,GAAGA,CAAC;EACpBC,aAAa;EACbC,gBAAgB;EAChBC,aAAa;EACbC,gBAAgB;EAChBC,WAAW;EACXC,cAAc;EACdC,WAAW;EACXC;AAUD,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,eAAA;EACL,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGvB,cAAc,CAAC,CAAC;EACzC,MAAM;IACLwB,oBAAoB;IACpBC,WAAW;IACXC,qBAAqB;IACrBC,sBAAsB;IACtBC,qBAAqB;IACrBC,OAAO;IACPC,sBAAsB;IACtBC,WAAW;IACXC,cAAc;IACdC,gBAAgB;IAChBC,oBAAoB;IACpBC,yBAAyB;IACzBC,KAAK;IACLC,YAAY;IACZC,UAAU;IACVC,WAAW;IACXC,mBAAmB;IACnBC,iBAAiB;IACjBC,kBAAkB;IAClBC,eAAe;IACfC,YAAY;IACZC,cAAc;IACdC,aAAa;IACbC,gBAAgB;IAChBC,WAAW;IACXC,KAAK;IACLC,wBAAwB;IACxBC,YAAY;IACZC,OAAO;IACPC,UAAU;IACVC,QAAQ;IACRC,WAAW;IACXC,aAAa;IACbC;EACD,CAAC,GAAGlE,cAAc,CAAEmE,KAAK,IAAKA,KAAK,CAAC;EAEpC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGpF,QAAQ,CAAqB,IAAI,CAAC;EAClE,MAAMqF,MAAM,GAAGtF,MAAM,CAA+C,IAAIuF,GAAG,CAAC,CAAC,CAAC;EAE9E,MAAM,CAACC,UAAU,EAAEC,YAAY,CAAC,GAAGxF,QAAQ,CAAoByF,SAAS,CAAC;EACzE,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG3F,QAAQ,CAAS,CAAC,CAAC;EAC3D,MAAM,CAAC4F,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG7F,QAAQ,CAAS,EAAE,CAAC;EAC1E,MAAM,CAAC8F,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG/F,QAAQ,CAAe,CAAC;EAC5E,MAAM,CAACgG,eAAe,EAAEC,kBAAkB,CAAC,GAAGjG,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACkG,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnG,QAAQ,CAA6B,CAAC,CAAC,CAAC;;EAE1F;EACA,MAAM,CAACoG,cAAc,EAAEC,iBAAiB,CAAC,GAAGrG,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAMsG,UAAU,GAAGvG,MAAM,CAAiB,IAAI,CAAC;EAC/C,MAAMwG,YAAY,GAAGxG,MAAM,CAAM,IAAI,CAAC;;EAEtC;EACA,MAAM,CAACyG,aAAa,EAAEC,gBAAgB,CAAC,GAAGzG,QAAQ,CAAC;IAClD8B,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,GAAG,EAAE;EACN,CAAC,CAAC;;EAEF;EACA,MAAM0E,eAAe,GAAIC,IAAkB,IAAc;IACxD;IACA,MAAMC,SAAS,GAAGD,IAAI,KAAK,KAAK,GAAG,KAAK,GAAGA,IAAI,KAAK,QAAQ,GAAG,QAAQ,GAAGA,IAAI,KAAK,OAAO,GAAG,OAAO,GAAG,IAAI;;IAE3G;IACA,IAAIC,SAAS,KAAK,IAAI,EAAE,OAAO,KAAK;IAEpC,OAAOJ,aAAa,CAACI,SAAS,CAAC,IAAI/E,YAAY,CAAC+E,SAAS,CAAC;EAC3D,CAAC;EACD,MAAMC,kBAAkB,GAAGA,CAACC,KAAoC,EAAEC,GAAW,KAAK;IACjF,IACAL,eAAe,CAAC,KAAK,CAAC,IACtBA,eAAe,CAAC,QAAQ,CAAC,IACzBA,eAAe,CAAC,OAAO,CAAC,EACtB;MACD;IACD;IACA,MAAMM,aAAa,GAAGF,KAAK,CAACE,aAAa;IACzCC,UAAU,CAAC,MAAM;MAChBtB,eAAe,CAACoB,GAAG,CAAC;MACpB3B,WAAW,CAAC4B,aAAa,CAAC;IAC3B,CAAC,EAAE,CAAC,CAAC;EACN,CAAC;EAED,MAAME,kBAAkB,GAAGA,CAAA,KAAM;IAChC9B,WAAW,CAAC,IAAI,CAAC;EAClB,CAAC;EAED,MAAM+B,WAAW,GAAIC,EAAU,IAAK;IACnCjF,gBAAgB,CAAC,IAAI,CAAC;IACtB0D,sBAAsB,CAACuB,EAAE,CAAC;EAC3B,CAAC;EAED,MAAMC,UAAU,GAAID,EAAU,IAAK;IAAA,IAAAE,mBAAA,EAAAC,qBAAA;IAClC,IAAIlC,MAAM,aAANA,MAAM,gBAAAiC,mBAAA,GAANjC,MAAM,CAAEmC,OAAO,CAACC,GAAG,CAACL,EAAE,CAAC,cAAAE,mBAAA,gBAAAC,qBAAA,GAAvBD,mBAAA,CAAyBE,OAAO,cAAAD,qBAAA,eAAhCA,qBAAA,CAAkCG,SAAS,EAAE;MAAA,IAAAC,oBAAA,EAAAC,qBAAA;MAChD1E,qBAAqB,CAACkE,EAAE,EAAE,EAAAO,oBAAA,GAAAtC,MAAM,CAACmC,OAAO,CAACC,GAAG,CAACL,EAAE,CAAC,cAAAO,oBAAA,wBAAAC,qBAAA,GAAtBD,oBAAA,CAAwBH,OAAO,cAAAI,qBAAA,uBAA/BA,qBAAA,CAAiCF,SAAS,CAACG,IAAI,CAAC,CAAC,KAAI,EAAE,CAAC;IACnF;EACD,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IACjC3E,sBAAsB,CAACyC,mBAAmB,CAAC;EAC5C,CAAC;EACD,MAAMmC,oBAAoB,GAAGA,CAAA,KAAM;IAClC3E,qBAAqB,CAACwC,mBAAmB,CAAC;EAC3C,CAAC;EACD,MAAM,CAACoC,QAAQ,EAAEC,WAAW,CAAC,GAAGjI,QAAQ,CAAgB,CAAC;IAAE2G,IAAI,EAAE;EAAQ,CAAC,EAAE;IAAEA,IAAI,EAAE;EAAO,CAAC,EAAE;IAAEA,IAAI,EAAE;EAAS,CAAC,CAAC,CAAC;EAClH,MAAM,CAACuB,aAAa,EAAEC,gBAAgB,CAAC,GAAGnI,QAAQ,CAAgB,IAAI,CAAC;EACvE,MAAM,CAACoI,YAAY,EAAEC,eAAe,CAAC,GAAGrI,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAMsI,eAAe,GAAIC,KAAa,IAAK;IAC1CJ,gBAAgB,CAACI,KAAK,CAAC;EACxB,CAAC;EACD,MAAMC,eAAe,GAAID,KAAa,IAAK;IAC1C,IAAIL,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAKK,KAAK,EAAE;MACtD,MAAME,iBAAiB,GAAG,CAAC,GAAGT,QAAQ,CAAC;MACvC,MAAM,CAACU,OAAO,CAAC,GAAGD,iBAAiB,CAACE,MAAM,CAACT,aAAa,EAAE,CAAC,CAAC;MAC5DO,iBAAiB,CAACE,MAAM,CAACJ,KAAK,EAAE,CAAC,EAAEG,OAAO,CAAC;MAC3CT,WAAW,CAACQ,iBAAiB,CAAC;MAC9BN,gBAAgB,CAACI,KAAK,CAAC;IACxB;EACD,CAAC;EAED,MAAMK,aAAa,GAAGA,CAAA,KAAM;IAC3BT,gBAAgB,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMU,WAAW,GAAIzB,EAAU,IAAK;IACnC,IAAI,CAAC/B,MAAM,CAACmC,OAAO,CAACsB,GAAG,CAAC1B,EAAE,CAAC,EAAE;MAC5B,MAAM2B,MAAM,gBAAGlJ,KAAK,CAACmJ,SAAS,CAAiB,CAAC;MAChD3D,MAAM,CAACmC,OAAO,CAACyB,GAAG,CAAC7B,EAAE,EAAE2B,MAAM,CAAC;MAC9B,OAAOA,MAAM;IACd;IACA,OAAO1D,MAAM,CAACmC,OAAO,CAACC,GAAG,CAACL,EAAE,CAAC;EAC9B,CAAC;;EAED;EACAtH,SAAS,CAAC,MAAM;IAAA,IAAAoJ,qBAAA;IACf,IAAIlG,oBAAoB,KAAAkG,qBAAA,GAAIlG,oBAAoB,CAACC,WAAW,GAAG,CAAC,CAAC,cAAAiG,qBAAA,eAArCA,qBAAA,CAAuCC,UAAU,EAAE;MAC9E,MAAMA,UAAU,GAAGnG,oBAAoB,CAACC,WAAW,GAAG,CAAC,CAAC,CAACkG,UAAU;MACnE,MAAMC,MAAM,GAAG;QACdtH,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTC,GAAG,EAAE;MACN,CAAC;;MAED;MACAmH,UAAU,CAACE,OAAO,CAACC,SAAS,IAAI;QAC/B,IAAIA,SAAS,CAAC3C,IAAI,KAAK,OAAO,EAAEyC,MAAM,CAACtH,KAAK,EAAE,CAAC,KAC1C,IAAIwH,SAAS,CAAC3C,IAAI,KAAK,QAAQ,EAAEyC,MAAM,CAACrH,MAAM,EAAE,CAAC,KACjD,IAAIuH,SAAS,CAAC3C,IAAI,KAAK,KAAK,EAAEyC,MAAM,CAACpH,GAAG,EAAE;MAChD,CAAC,CAAC;MAEFyE,gBAAgB,CAAC2C,MAAM,CAAC;IACzB;EACD,CAAC,EAAE,CAACpG,oBAAoB,EAAEC,WAAW,CAAC,CAAC;;EAEvC;EACAnD,SAAS,CAAC,MAAM;IACf,MAAMyJ,iBAAiB,GAAGA,CAAA,KAAM;MAC/B,IAAIjD,UAAU,CAACkB,OAAO,EAAE;QACvB;QACAlB,UAAU,CAACkB,OAAO,CAACgC,KAAK,CAACC,MAAM,GAAG,MAAM;QACxC,MAAMC,aAAa,GAAGpD,UAAU,CAACkB,OAAO,CAACmC,YAAY;QACrD,MAAMC,eAAe,GAAG,GAAG,CAAC,CAAC;QAC7B,MAAMC,YAAY,GAAGH,aAAa,GAAGE,eAAe;QAGpDvD,iBAAiB,CAACwD,YAAY,CAAC;;QAE/B;QACA,IAAItD,YAAY,CAACiB,OAAO,EAAE;UACzB;UACA,IAAIjB,YAAY,CAACiB,OAAO,CAACsC,YAAY,EAAE;YACtCvD,YAAY,CAACiB,OAAO,CAACsC,YAAY,CAAC,CAAC;UACpC;UACA;UACA7C,UAAU,CAAC,MAAM;YAChB,IAAIV,YAAY,CAACiB,OAAO,IAAIjB,YAAY,CAACiB,OAAO,CAACsC,YAAY,EAAE;cAC9DvD,YAAY,CAACiB,OAAO,CAACsC,YAAY,CAAC,CAAC;YACpC;UACD,CAAC,EAAE,EAAE,CAAC;QACP;MACD;IACD,CAAC;IAGDP,iBAAiB,CAAC,CAAC;IAGnB,MAAMQ,QAAQ,GAAG,CAChB9C,UAAU,CAACsC,iBAAiB,EAAE,EAAE,CAAC,EACjCtC,UAAU,CAACsC,iBAAiB,EAAE,GAAG,CAAC,EAClCtC,UAAU,CAACsC,iBAAiB,EAAE,GAAG,CAAC,EAClCtC,UAAU,CAACsC,iBAAiB,EAAE,GAAG,CAAC,CAClC;IAGD,IAAIS,cAAqC,GAAG,IAAI;IAChD,IAAIC,gBAAyC,GAAG,IAAI;IAEpD,IAAI3D,UAAU,CAACkB,OAAO,IAAI0C,MAAM,CAACC,cAAc,EAAE;MAChDH,cAAc,GAAG,IAAIG,cAAc,CAAC,MAAM;QACzClD,UAAU,CAACsC,iBAAiB,EAAE,EAAE,CAAC;MAClC,CAAC,CAAC;MACFS,cAAc,CAACI,OAAO,CAAC9D,UAAU,CAACkB,OAAO,CAAC;IAC3C;IAGA,IAAIlB,UAAU,CAACkB,OAAO,IAAI0C,MAAM,CAACG,gBAAgB,EAAE;MAClDJ,gBAAgB,GAAG,IAAII,gBAAgB,CAAC,MAAM;QAC7CpD,UAAU,CAACsC,iBAAiB,EAAE,EAAE,CAAC;MAClC,CAAC,CAAC;MACFU,gBAAgB,CAACG,OAAO,CAAC9D,UAAU,CAACkB,OAAO,EAAE;QAC5C8C,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE,IAAI;QACbC,UAAU,EAAE,IAAI;QAChBC,eAAe,EAAE,CAAC,OAAO,EAAE,OAAO;MACnC,CAAC,CAAC;IACH;IAEA,OAAO,MAAM;MACZV,QAAQ,CAACV,OAAO,CAACqB,YAAY,CAAC;MAC9B,IAAIV,cAAc,EAAE;QACnBA,cAAc,CAACW,UAAU,CAAC,CAAC;MAC5B;MACA,IAAIV,gBAAgB,EAAE;QACrBA,gBAAgB,CAACU,UAAU,CAAC,CAAC;MAC9B;IACD,CAAC;EACF,CAAC,EAAE,CAAC3H,oBAAoB,EAAEC,WAAW,CAAC,CAAC;EAEvCnD,SAAS,CAAC,MAAM;IACf,IAAIyD,WAAW,EAAE;MAChB,IAAI,OAAO2G,MAAM,KAAK,WAAW,EAAE;QAAA,IAAAU,sBAAA,EAAAC,sBAAA;QAClC,MAAMC,KAAK,IAAAF,sBAAA,GAAG5H,oBAAoB,CAACC,WAAW,GAAG,CAAC,CAAC,cAAA2H,sBAAA,wBAAAC,sBAAA,GAArCD,sBAAA,CAAuCE,KAAK,cAAAD,sBAAA,uBAA5CA,sBAAA,CAA8CE,KAAK;;QAEjE;QACA,IAAID,KAAK,IAAIA,KAAK,CAACjD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;UACjC,MAAMmD,MAAM,GAAGd,MAAM,CAACe,QAAQ,CAACC,QAAQ,CACtCJ,KAAK,EACLZ,MAAM,CAACe,QAAQ,EACf,IAAI,EACJE,WAAW,CAACC,uBAAuB,EACnC,IACD,CAAC;UAED,MAAMC,OAAO,GAAGL,MAAM,CAACM,eAAqC;UAC5D,IAAID,OAAO,EAAE;YAAA,IAAAE,oBAAA;YACZ7G,wBAAwB,CAAC2G,OAAO,CAAC;YACjC,MAAMG,IAAI,GAAGH,OAAO,CAACI,qBAAqB,CAAC,CAAC;YAC5C,MAAMC,IAAI,GAAG;cACZL,OAAO;cACPM,OAAO,EAAEN,OAAO,CAACM,OAAO;cACxBC,OAAO,EAAEP,OAAO,CAACQ,SAAS;cAC1BzE,EAAE,EAAEiE,OAAO,CAACjE,EAAE;cACd0E,QAAQ,EAAE;gBACTC,CAAC,EAAEP,IAAI,CAACO,CAAC;gBACTC,CAAC,EAAER,IAAI,CAACQ,CAAC;gBACTpI,KAAK,EAAE4H,IAAI,CAAC5H,KAAK;gBACjB6F,MAAM,EAAE+B,IAAI,CAAC/B;cACd,CAAC;cACDwC,WAAW,EAAE,EAAAV,oBAAA,GAAAF,OAAO,CAACY,WAAW,cAAAV,oBAAA,uBAAnBA,oBAAA,CAAqB1D,IAAI,CAAC,CAAC,KAAI;YAC7C,CAAC;YACDlE,yBAAyB,CAAC+H,IAAI,CAAC;UAChC;QACD;MACD;MACAzE,UAAU,CAAC,MAAM;QAChB,IAAIiF,KAAK,GAAGjB,QAAQ,CAACkB,aAAa,CAAC,+BAA+B,CAAC;QACnE,IAAID,KAAK,YAAYE,WAAW,IAAIhK,aAAa,EAAE;UAClD;UACA;QAAA;MAEF,CAAC,EAAE,EAAE,CAAC;IACP;EACD,CAAC,EAAE,CAACmB,WAAW,CAAC,CAAC;EAEjB,MAAM8I,gBAAgB,IAAA1J,sBAAA,GAAGK,oBAAoB,CAACC,WAAW,GAAG,CAAC,CAAC,cAAAN,sBAAA,uBAArCA,sBAAA,CAAuC2J,MAAM;EAEtE,oBACC5K,OAAA;IAAA6K,QAAA,gBACA7K,OAAA;MAAK8H,KAAK,EAAE;QAAEgD,YAAY,EAAE,KAAK;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAAF,QAAA,EAClD,CAACrK,aAAa,IAAI0C,OAAO,iBACzBlD,OAAA,CAACvB,UAAU;QAAC0L,SAAS,EAAC,eAAe;QAEpC,cAAW,OAAO;QAAAU,QAAA,eAElB7K,OAAA,CAACN,SAAS;UAACsL,EAAE,EAAE;YAAEC,IAAI,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAO;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC;IACZ;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAENtL,OAAA,CAACH,gBAAgB;MAEhB0L,GAAG,EAAE1G,YAAa;MAClBiD,KAAK,EAAE;QAAE0D,SAAS,EAAE;MAAQ,CAAE;MAC9BC,OAAO,EAAE;QACRC,eAAe,EAAE,CAAChH,cAAc;QAChCiH,eAAe,EAAE,IAAI;QACrBC,gBAAgB,EAAE,KAAK;QACvBC,WAAW,EAAE,IAAI;QACjBC,kBAAkB,EAAE,EAAE;QACtBC,kBAAkB,EAAE,IAAI;QACxBC,mBAAmB,EAAE;MACtB,CAAE;MAAAnB,QAAA,eAEJ7K,OAAA;QACCuL,GAAG,EAAE3G,UAAW;QAChBkD,KAAK,EAAE;UACN;UACA;UACA;UACA;UACA;UACA;UACAmE,SAAS,EAAE,EAAE;UACb;UACA;UACAC,QAAQ,EAAE,QAAQ;UAClB9B,QAAQ,EAAE;QACX,CAAE;QAAAS,QAAA,IAAA3J,sBAAA,GAGDI,oBAAoB,CAACC,WAAW,GAAG,CAAC,CAAC,cAAAL,sBAAA,uBAArCA,sBAAA,CAAuCuG,UAAU,CAAC0E,GAAG,CAAC,CAACC,IAAI,EAAEvF,KAAK,KAAK;UACvE,MAAMnB,EAAE,GAAG,GAAG0G,IAAI,CAACnH,IAAI,IAAImH,IAAI,CAAC1G,EAAE,EAAE;UACpC,MAAMkB,eAAe,GAAIC,KAAa,IAAK;YAC1C,IAAIH,YAAY,KAAKG,KAAK,EAAE,OAAO,CAAC;YACpCF,eAAe,CAACE,KAAK,CAAC;UACvB,CAAC;UAED,MAAMC,eAAe,GAAID,KAAa,IAAK;YAC1C,IAAIH,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAKG,KAAK,EAAE;cACpD,MAAMwF,iBAAiB,GAAG,CAAC,GAAG/K,oBAAoB,CAACC,WAAW,GAAG,CAAC,CAAC,CAACkG,UAAU,CAAC;cAC/E,MAAM,CAAC6E,WAAW,CAAC,GAAGD,iBAAiB,CAACpF,MAAM,CAACP,YAAY,EAAE,CAAC,CAAC;cAC/D2F,iBAAiB,CAACpF,MAAM,CAACJ,KAAK,EAAE,CAAC,EAAEyF,WAAW,CAAC;;cAE/C;cACAjN,cAAc,CAACkN,QAAQ,CAAC;gBACvBjL,oBAAoB,EAAE,CACrB;kBACC,GAAGA,oBAAoB,CAACC,WAAW,GAAG,CAAC,CAAC;kBACxCkG,UAAU,EAAE4E;gBACb,CAAC;cAEH,CAAC,CAAC;cAEF1F,eAAe,CAACE,KAAK,CAAC;YACvB;UACD,CAAC;UAED,MAAMK,aAAa,GAAGA,CAAA,KAAM;YAC3BP,eAAe,CAAC,IAAI,CAAC;UACtB,CAAC;UACD,oBACC3G,OAAA,CAAAE,SAAA;YAAA2K,QAAA,eACC7K,OAAA;cACCwM,YAAY,EAAGC,CAAC,IAAK;gBACpBpI,qBAAqB,CAACoI,CAAC,CAACnH,aAAa,CAACI,EAAkB,CAAC;cAC1D,CAAE;cACFoC,KAAK,EAAE;gBACNsC,QAAQ,EAAE,UAAU;gBACpBsC,OAAO,EAAE,OAAO;gBAChBC,iBAAiB,EAAEvI,kBAAkB,KAAKsB,EAAE,GAAG,KAAK,GAAG,KAAK;gBAC5DkH,iBAAiB,EAAExI,kBAAkB,KAAKsB,EAAE,GAAG,qBAAqB,GAAG,aAAa;gBACpFmH,iBAAiB,EAAEzI,kBAAkB,KAAKsB,EAAE,GAAG,QAAQ,GAAG,MAAM;gBAChEoH,YAAY,EAAC;cACd,CAAE;cACFpH,EAAE,EAAEA,EAAG;cACPqH,SAAS,EAAEX,IAAI,CAACnH,IAAI,KAAK,KAAM;cAC/B+H,WAAW,EAAEA,CAAA,KAAMpG,eAAe,CAACC,KAAK,CAAE;cAC1CoG,WAAW,EAAEA,CAAA,KAAMnG,eAAe,CAACD,KAAK,CAAE;cAC1CqG,SAAS,EAAEhG,aAAc;cAAA2D,QAAA,EAExBuB,IAAI,CAACnH,IAAI,KAAK,QAAQ,gBACtBjF,OAAA,CAAAE,SAAA;gBAAA2K,QAAA,gBACC7K,OAAA,CAACV,aAAa;kBACb6N,KAAK,EAAEf,IAAK;kBACZrL,gBAAgB,EAAEA,gBAAiB;kBACnCqM,eAAe,EAAEpI,eAAe,CAAC,QAAQ;gBAAE;kBAAAmG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,EACDlH,kBAAkB,KAAKsB,EAAE,iBACzB1F,OAAA,CAACqN,cAAc;kBAAClI,kBAAkB,EAAGsH,CAAC,IAAKtH,kBAAkB,CAACsH,CAAC,EAAE5F,KAAK;gBAAE;kBAAAsE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAC1E;cAAA,eACA,CAAC,GACAc,IAAI,CAACnH,IAAI,KAAK,OAAO,gBACxBjF,OAAA,CAAAE,SAAA;gBAAA2K,QAAA,gBACC7K,OAAA,CAACT,YAAY;kBACZ4N,KAAK,EAAEf,IAAK;kBACZgB,eAAe,EAAEpI,eAAe,CAAC,OAAO;gBAAE;kBAAAmG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC,EACDlH,kBAAkB,KAAKsB,EAAE,iBACzB1F,OAAA,CAACqN,cAAc;kBAAClI,kBAAkB,EAAGsH,CAAC,IAAKtH,kBAAkB,CAACsH,CAAC,EAAE5F,KAAK;gBAAE;kBAAAsE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAC1E;cAAA,eACA,CAAC,GACAc,IAAI,CAACnH,IAAI,KAAK,KAAK,gBACtBjF,OAAA,CAAAE,SAAA;gBAAA2K,QAAA,gBACC7K,OAAA,CAACR,UAAU;kBACV2N,KAAK,EAAEf;kBACP;kBAAA;kBACAzI,MAAM,EAAEwD,WAAW,CAACiF,IAAI,CAAC1G,EAAE,CAAE;kBAC7BD,WAAW,EAAEA,WAAY;kBACzB6H,WAAW,EAAE3H,UAAW;kBACxBnF,aAAa,EAAEA,aAAc;kBAC7BC,gBAAgB,EAAEA,gBAAiB;kBACnCyD,mBAAmB,EAAEA,mBAAoB;kBACzCkJ,eAAe,EAAEpI,eAAe,CAAC,KAAK;gBAAE;kBAAAmG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,EACDlH,kBAAkB,KAAKsB,EAAE,iBACzB1F,OAAA,CAACqN,cAAc;kBAAClI,kBAAkB,EAAGsH,CAAC,IAAKtH,kBAAkB,CAACsH,CAAC,EAAE5F,KAAK;gBAAE;kBAAAsE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAC1E;cAAA,eACA,CAAC,GACA;YAAI;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC,gBACL,CAAC;QAEL,CAAC,CAAC,eAKFtL,OAAA,CAACuN,cAAc;UACd9J,QAAQ,EAAEA,QAAS;UACnB+B,kBAAkB,EAAEA,kBAAmB;UACvCxB,YAAY,EAAEA,YAAa;UAC3BgB,eAAe,EAAEA;QAAgB;UAAAmG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,EAED3K,gBAAgB,IAAIC,WAAW,iBAC/BZ,OAAA,CAACJ,UAAU;UACVgB,WAAW,EAAEA,WAAY;UACzBC,cAAc,EAAEA,cAAe;UAC/BC,WAAW,EAAEA;QAAY;UAAAqK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CACD,EACA9K,aAAa,IAAI0D,mBAAmB,KAAIP,MAAM,aAANA,MAAM,wBAAAxC,eAAA,GAANwC,MAAM,CAAEmC,OAAO,cAAA3E,eAAA,uBAAfA,eAAA,CAAiB4E,GAAG,CAAC7B,mBAAmB,CAAC,kBACjFlE,OAAA,CAACwN,UAAU;UACVhN,aAAa,EAAEA,aAAc;UAC7BC,gBAAgB,EAAEA,gBAAiB;UACnCkD,MAAM,EAAEA,MAAM,CAACmC,OAAO,CAACC,GAAG,CAAC7B,mBAAmB,CAAE;UAChDA,mBAAmB,EAAEA,mBAAoB;UACzCkC,mBAAmB,EAAEA,mBAAoB;UACzCC,oBAAoB,EAAEA,oBAAqB;UAC3CxC,UAAU,EAAEA,UAAW;UACvBC,YAAY,EAAEA,YAAa;UAC3BnC,OAAO,EAAEA,OAAQ;UACjBjB,aAAa,EAAEA,aAAc;UAC7Bc,qBAAqB,EAAEA,qBAAsB;UAC7CiM,eAAe,EAAEnJ,eAAgB;UACjCC,kBAAkB,EAAEA,kBAAmB;UACvCmJ,gBAAgB,EAAE/C,gBAAiB;UACjCyC,eAAe,EAAEpI,eAAe,CAAC,KAAK;QAAE;UAAAmG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CACD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC,GAhKD,aAAa5G,cAAc,EAAE;MAAAyG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAiKhB,CAAC,EACpBlI,QAAQ,IAAI9B,oBAAoB,CAACqM,MAAM,GAAC,CAAC,KACvC5L,gBAAgB,KAAK,SAAS,IAAKC,oBAAoB,KAAK,SAAS,IAAIA,oBAAoB,KAAK,SAAS,CAAC,KAC5GF,cAAc,KAAK,CAAC,IAAIA,cAAc,KAAK,EAAE,gBAC7C9B,OAAA,CAAC4N,WAAW;MACXC,UAAU,EAAEtM,WAAY;MACxBwB,KAAK,EAAEA,KAAK,CAAC4K,MAAO;MACpBrK,aAAa,EAAIA;IAAc;MAAA6H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC,GACCxJ,cAAc,KAAK,CAAC,gBACvB9B,OAAA;MAAA6K,QAAA,eACC7K,OAAA,CAAClB,cAAc;QACdgP,OAAO,EAAC;QACR;QAAA;QACAzE,KAAK,EAAG9H,WAAW,GAAGwB,KAAK,CAAC4K,MAAM,GAAI,GAAI;QAC1C3C,EAAE,EAAE;UACHjD,MAAM,EAAE,KAAK;UACb5F,YAAY,EAAE,MAAM;UACpB4L,MAAM,EAAE,UAAU;UAClB,0BAA0B,EAAE;YACJC,eAAe,EAAE1K,aAAa,CAAE;UAClC;QAAE;MAAE;QAAA6H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,GAELxJ,cAAc,KAAK,CAAC,gBACrB9B,OAAA;MAAK8H,KAAK,EAAE;QAAC4E,OAAO,EAAC;MAAK,CAAE;MAAA7B,QAAA,eAC3B7K,OAAA,CAACiO,iBAAiB;QAClBJ,UAAU,EAAEtM,WAAY;QACxBwB,KAAK,EAAEA,KAAK,CAAC4K,MAAO;QACpBrK,aAAa,EAAEA;MAAc;QAAA6H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE7B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,GACHxJ,cAAc,KAAK,CAAC,gBACvB9B,OAAA,CAAChB,WAAW;MACX,cAAW,YAAY;MACvBgM,EAAE,EAAE;QAAE0B,OAAO,EAAE;MAAM,CAAE;MAAA7B,QAAA,eAEvB7K,OAAA,CAACnB,UAAU;QAACmM,EAAE,EAAE;UAACE,KAAK,EAAG5H;QAAa,CAAE;QAAAuH,QAAA,GAAC,OACnC,EAACtJ,WAAW,EAAC,MAAI,EAACwB,KAAK,CAAC4K,MAAM;MAAA;QAAAxC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,GACX,IAAI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAER,CAAC;AAACtK,EAAA,CAvgBIT,WAAW;EAAA,QAmBST,cAAc,EAoCnCT,cAAc;AAAA;AAAA6O,EAAA,GAvDb3N,WAAW;AAwgBjB,eAAeA,WAAW;AAE1B,MAAM8M,cAAc,GAAGA,CAAC;EACvBlI;AAGD,CAAC,KAAK;EACL,oBACCnF,OAAA,CAACxB,GAAG;IAACwM,EAAE,EAAE;MAAEZ,QAAQ,EAAE,UAAU;MAAE+D,SAAS,EAAE,sBAAsB;MAAEC,GAAG,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAM,CAAE;IAAAxD,QAAA,eAC7F7K,OAAA,CAACvB,UAAU;MACV6P,OAAO,EAAEnJ,kBAAmB;MAC5B6F,EAAE,EAAE;QACHgD,eAAe,EAAE,SAAS;QAC1B,SAAS,EAAE;UACVA,eAAe,EAAE;QAClB,CAAC;QACD7L,YAAY,EAAE,KAAK;QACnBuK,OAAO,EAAE;MACV,CAAE;MAAA7B,QAAA,eAEF7K,OAAA,CAACf,OAAO;QACPsP,QAAQ,EAAC,OAAO;QAChBvD,EAAE,EAAE;UAAEE,KAAK,EAAE;QAAO;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAER,CAAC;AAACkD,GAAA,GAzBInB,cAAc;AA2BpB,MAAME,cAAc,GAAGA,CAAC;EACvB9J,QAAQ;EACR+B,kBAAkB;EAClBxB,YAAY;EACZgB;AAMD,CAAC,KAAK;EAAAyJ,GAAA;EACL,MAAM;IAAErN,CAAC,EAAEC;EAAU,CAAC,GAAGvB,cAAc,CAAC,CAAC;EACzC,MAAM;IAAE4O,qBAAqB;IAAE5M,cAAc;IAAEP,WAAW;IAAEwB,KAAK;IAAEF;EAAiB,CAAC,GAAGxD,cAAc,CACpGmE,KAAU,IAAKA,KACjB,CAAC;EACD,MAAMmL,gBAAgB,GAAIC,WAAyB,IAAK;IACvD;IACA,IAAI5J,eAAe,CAAC4J,WAAW,CAAC,EAAE;MAEjC;IACD;IAEAF,qBAAqB,CAACE,WAAW,EAAE5K,YAAY,CAAC;IAChDwB,kBAAkB,CAAC,CAAC;EACrB,CAAC;EAED,oBACCxF,OAAA,CAAAE,SAAA;IAAA2K,QAAA,EACEpH,QAAQ,iBACRzD,OAAA,CAACrB,OAAO;MACPkQ,IAAI,EAAEC,OAAO,CAACrL,QAAQ,CAAE;MACxBA,QAAQ,EAAEA,QAAS;MACnBsL,OAAO,EAAEvJ,kBAAmB;MAC5BwJ,YAAY,EAAE;QACbC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE;MACb,CAAE;MACFC,eAAe,EAAE;QAChBF,QAAQ,EAAE,KAAK;QACfC,UAAU,EAAE;MACb,CAAE;MACFxJ,EAAE,EAAC,yBAAyB;MAC5B0J,SAAS,EAAE;QACVC,KAAK,EAAE;UACNrE,EAAE,EAAE;YACJ0B,OAAO,EAAE,MAAM;YACf3B,OAAO,EAAE,MAAM;YACfuE,GAAG,EAAE,MAAM;YACXpN,KAAK,EAAE;UACR;QACA,CAAC;QACDqN,IAAI,EAAE;UACL;UACAvE,EAAE,EAAE;YACHwE,MAAM,EAAGC,KAAK,IAAKA,KAAK,CAACD,MAAM,CAAC7N,OAAO,GAAG;UAC3C;QACD;MACD,CAAE;MACF+N,mBAAmB,EAAE,IAAK;MAAA7E,QAAA,eAE1B7K,OAAA,CAACxB,GAAG;QACHuM,OAAO,EAAC,MAAM;QACd4E,aAAa,EAAC,KAAK;QACnBL,GAAG,EAAC,MAAM;QAAAzE,QAAA,gBAEV7K,OAAA,CAACpB,OAAO;UAACgR,KAAK;UACbC,KAAK,EAAE7K,eAAe,CAAC,KAAK,CAAC,GAAG3D,SAAS,CAAC,+CAA+C,EAAE;YAAEyO,YAAY,EAAE;UAAgD,CAAC,CAAC,GAAG,EAAG;UACnKC,WAAW,EAAE;YACZ/E,EAAE,EAAE;cACHwE,MAAM,EAAE;YACT;UACD,CAAE;UAAA3E,QAAA,eAEF7K,OAAA,CAACxB,GAAG;YACHuM,OAAO,EAAC,MAAM;YACd4E,aAAa,EAAC,QAAQ;YACtBK,UAAU,EAAC,QAAQ;YACnBhF,EAAE,EAAE;cACHiF,MAAM,EAAEjL,eAAe,CAAC,KAAK,CAAC,GAAG,aAAa,GAAG,SAAS;cAC1DkL,OAAO,EAAElL,eAAe,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;cACzCmL,GAAG,EAAE;gBACJ5B,QAAQ,EAAE;cACX;YACD,CAAE;YACFD,OAAO,EAAEA,CAAA,KAAM,CAACtJ,eAAe,CAAC,KAAK,CAAC,IAAI2J,gBAAgB,CAAC,KAAK,CAAE;YAAA9D,QAAA,gBAElE7K,OAAA,CAACb,UAAU;cAAAgM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACdtL,OAAA,CAACnB,UAAU;cAACmM,EAAE,EAAE;gBAAEuD,QAAQ,EAAE;cAAkB,CAAE;cAAA1D,QAAA,EAAExJ,SAAS,CAAC,WAAW,EAAE;gBAAEyO,YAAY,EAAE;cAAY,CAAC;YAAC;cAAA3E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEVtL,OAAA,CAACpB,OAAO;UAACgR,KAAK;UACbC,KAAK,EAAE7K,eAAe,CAAC,QAAQ,CAAC,GAAG3D,SAAS,CAAC,4CAA4C,EAAE;YAAEyO,YAAY,EAAE;UAA6C,CAAC,CAAC,GAAG,EAAG;UAChKC,WAAW,EAAE;YACZ/E,EAAE,EAAE;cACHwE,MAAM,EAAE;YACT;UACD,CAAE;UAAA3E,QAAA,eAEF7K,OAAA,CAACxB,GAAG;YACHuM,OAAO,EAAC,MAAM;YACd4E,aAAa,EAAC,QAAQ;YACtBK,UAAU,EAAC,QAAQ;YACnBhF,EAAE,EAAE;cACHiF,MAAM,EAAEjL,eAAe,CAAC,QAAQ,CAAC,GAAG,aAAa,GAAG,SAAS;cAC7DkL,OAAO,EAAElL,eAAe,CAAC,QAAQ,CAAC,GAAG,GAAG,GAAG,CAAC;cAC5CmL,GAAG,EAAE;gBACJ5B,QAAQ,EAAE;cACX;YACD,CAAE;YACFD,OAAO,EAAEA,CAAA,KAAM,CAACtJ,eAAe,CAAC,QAAQ,CAAC,IAAI2J,gBAAgB,CAAC,QAAQ,CAAE;YAAA9D,QAAA,gBAExE7K,OAAA,CAACZ,IAAI;cAAA+L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACRtL,OAAA,CAACnB,UAAU;cAACmM,EAAE,EAAE;gBAAEuD,QAAQ,EAAE;cAAkB,CAAE;cAAA1D,QAAA,EAAExJ,SAAS,CAAC,QAAQ,EAAE;gBAAEyO,YAAY,EAAE;cAAS,CAAC;YAAC;cAAA3E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3G;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEVtL,OAAA,CAACpB,OAAO;UAACgR,KAAK;UACbC,KAAK,EAAE7K,eAAe,CAAC,OAAO,CAAC,GAAG3D,SAAS,CAAC,2CAA2C,EAAE;YAAEyO,YAAY,EAAE;UAA4C,CAAC,CAAC,GAAG,EAAG;UAC7JC,WAAW,EAAE;YACZ/E,EAAE,EAAE;cACHwE,MAAM,EAAE;YACT;UACD,CAAE;UAAA3E,QAAA,eAEF7K,OAAA,CAACxB,GAAG;YACHuM,OAAO,EAAC,MAAM;YACd4E,aAAa,EAAC,QAAQ;YACtBK,UAAU,EAAC,QAAQ;YACnBhF,EAAE,EAAE;cACHiF,MAAM,EAAEjL,eAAe,CAAC,OAAO,CAAC,GAAG,aAAa,GAAG,SAAS;cAC5DkL,OAAO,EAAElL,eAAe,CAAC,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC;cAC3CmL,GAAG,EAAE;gBACJ5B,QAAQ,EAAE;cACX;YACD,CAAE;YACFD,OAAO,EAAEA,CAAA,KAAM,CAACtJ,eAAe,CAAC,OAAO,CAAC,IAAI2J,gBAAgB,CAAC,OAAO,CAAE;YAAA9D,QAAA,gBAEtE7K,OAAA,CAACd,KAAK;cAAAiM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACTtL,OAAA,CAACnB,UAAU;cAACmM,EAAE,EAAE;gBAAEuD,QAAQ,EAAE;cAAkB,CAAE;cAAA1D,QAAA,EAAExJ,SAAS,CAAC,OAAO,EAAE;gBAAEyO,YAAY,EAAE;cAAQ,CAAC;YAAC;cAAA3E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAsDN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EACT,gBACA,CAAC;AAEL,CAAC;AAACmD,GAAA,CAxMIlB,cAAc;EAAA,QAWMzN,cAAc,EACiDT,cAAc;AAAA;AAAA+Q,GAAA,GAZjG7C,cAAc;AA0MpB,MAAMC,UAAU,GAAGA,CAAC;EACnBhN,aAAa;EACb0D,mBAAmB;EACnBvC,OAAO;EACPlB,gBAAgB;EAChBkD,MAAM;EACNE,UAAU;EACVuC,mBAAmB;EACnBC,oBAAoB;EACpBvC,YAAY;EACZpD,aAAa;EACbc,qBAAqB;EACrBiM,eAAe;EACflJ,kBAAkB;EAClBmJ,gBAAgB;EAChBN;AAkBD,CAAC,KAAK;EAAAiD,GAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,gBAAA;EACL;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;;EAEA;EACA;;EAEA;EACA;EACA;;EAEA;;EAEA;;EAEA;;EAEA;EACA;EACA;EACA;;EAEA,MAAMlL,WAAW,GAAGA,CAAA,KAAM;IACzBlB,kBAAkB,CAAC,IAAI,CAAC;IACxBqM,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;EACjC,CAAC;EACD,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzS,QAAQ,CAAU,KAAK,CAAC;EACpEF,SAAS,CAAC,MAAM;IACb,MAAM4S,GAAG,GAAGzH,QAAQ,CAAC0H,IAAI,CAACC,YAAY,CAAC,KAAK,CAAC,IAAI,KAAK;IACtDH,iBAAiB,CAACC,GAAG,CAACG,WAAW,CAAC,CAAC,KAAK,KAAK,CAAC;EAClD,CAAC,EAAE,EAAE,CAAC;EACL,MAAMC,YAAY,GAAG7S,OAAO,CAC3B,OAAO;IACN8S,SAAS,EAAEP,cAAc,GAAG,KAAK,GAAY,KAAc;IAE3D;IACEQ,QAAQ,EAAG,IAAI;IAAE;IACnBC,aAAa,EAAE,KAAK;IACpBC,eAAe,EAAE,KAAK;IACtBC,SAAS,EAAE,IAAI;IAAE;IACjBC,OAAO,EAAE,CACR,MAAM,EACN,QAAQ,EACR,WAAW,EACX,OAAO,EACP,MAAM,EACN,UAAU,EACV,MAAM,EACN;MACCC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,+DAA+D;MACxEC,IAAI,EAAE,CACL,QAAQ,EACR,eAAe,EACf,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,MAAM,EACN,MAAM,EACN,GAAG,EACH,IAAI,EACJ,QAAQ,EACR,YAAY,EACZ,QAAQ,EACR,UAAU,EACV,OAAO,EACP,aAAa,EACb,WAAW,EACX,GAAG,EACH,SAAS,EACT,QAAQ,EACR,WAAW;IAEb,CAAC,CACD;IACDC,MAAM,EAAE;MACPC,KAAK,EAAEtM,WAAW;MAClB;MACAuM,SAAS,EAAGC,MAAW,IAAK;QAC3B;QACA1M,UAAU,CAAC,MAAM;UAChB0M,MAAM,CAACF,KAAK,CAAC,CAAC;QACf,CAAC,EAAE,CAAC,CAAC;MACN;IACD,CAAC;IACDvG,SAAS,EAAE;EACZ,CAAC,CAAC,EACF,CAACsF,cAAc,CAChB,CAAC;EAED,oBACC9Q,OAAA,CAAAE,SAAA;IAAA2K,QAAA,EACErK,aAAa,iBACbf,YAAY;IAAA;IACX;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAO,OAAA,CAACtB,KAAK;MACLoJ,KAAK,EAAE;QACNsC,QAAQ,EAAE,UAAU;QACpBoF,MAAM,EAAE,KAAK;QACbnB,IAAI,EAAE,EAAAiC,qBAAA,IAAAC,sBAAA,GAAChH,QAAQ,CAAC2I,cAAc,CAAC,gBAAgB,CAAC,cAAA3B,sBAAA,wBAAAC,sBAAA,GAAzCD,sBAAA,CAA2CxG,qBAAqB,CAAC,CAAC,cAAAyG,sBAAA,uBAAlEA,sBAAA,CAAoEnG,CAAC,cAAAiG,qBAAA,cAAAA,qBAAA,GAAI,GAAG,IAAI,CAAC;QACxF;QACAlC,GAAG,EAAE,EAAAqC,sBAAA,GAAAlH,QAAQ,CAAC2I,cAAc,CAAC,gBAAgB,CAAC,cAAAzB,sBAAA,wBAAAC,sBAAA,GAAzCD,sBAAA,CAA2C1G,qBAAqB,CAAC,CAAC,cAAA2G,sBAAA,uBAAlEA,sBAAA,CAAoEpG,CAAC,IAAG,EAAE,IAAI,EAAE;QACrFpI,KAAK,EAAE,CAAAwL,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAExL,KAAK,KAAI;MACnC,CAAE;MACFiQ,SAAS,EAAE,KAAM;MACjBzM,EAAE,EAAC,mBAAmB;MACtB0M,IAAI,EAAC,cAAc;MACnBpH,EAAE,EAAE;QACH,0BAA0B,EAAE;UAC3BD,OAAO,EAAE;QACV,CAAC;QACD,4BAA4B,EAAE;UAC7BsH,aAAa,EAAE;QAChB,CAAC;QACD,gCAAgC,EAAE;UACjCC,QAAQ,EAAE;QACX,CAAC;QACD,oBAAoB,EAAE;UACrB9G,SAAS,EAAE,gCAAgC;UAC3C+G,SAAS,EAAE;QACZ;MACD,CAAE;MAAA1H,QAAA,eAEF7K,OAAA,CAACL,WAAW;QACX0J,KAAK,EAAE,CAAA1F,MAAM,aAANA,MAAM,wBAAAgN,gBAAA,GAANhN,MAAM,CAAEmC,OAAO,cAAA6K,gBAAA,uBAAfA,gBAAA,CAAiB3K,SAAS,KAAI,EAAG;QACxCmE,SAAS,EAAC,aAAa;QACvBqI,MAAM,EAAE;UACP,GAAGpB,YAAY;UACfqB,QAAQ,EAAE;YACTC,IAAI,EAAE;cACLb,IAAI,EAAE;gBACL,qBAAqB,EAAE,SAAS;gBAChC,oBAAoB,EAAE,QAAQ;gBAC9B,2BAA2B,EAAE,eAAe;gBAC5C,uBAAuB,EAAE,WAAW;gBACpC,qBAAqB,EAAE,SAAS;gBAChC,4BAA4B,EAAE;cAC/B;YACD;UACD;QACD,CAAE;QACFc,MAAM,EAAEA,CAACC,UAAU,EAAExN,KAAK,KAAK;UAAA,IAAAyN,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;UAC9B,MAAMC,aAAa,IAAAJ,qBAAA,GAAGtJ,QAAQ,CAACkB,aAAa,CAAC,cAAc,CAAC,cAAAoI,qBAAA,uBAAtCA,qBAAA,CAAwCK,QAAQ,CAAC9N,KAAK,CAAC+N,MAAc,CAAC;UAC5F,MAAMC,kBAAkB,IAAAN,sBAAA,GAAGvJ,QAAQ,CAACkB,aAAa,CAAC,gBAAgB,CAAC,cAAAqI,sBAAA,uBAAxCA,sBAAA,CAA0CI,QAAQ,CAAC9N,KAAK,CAAC+N,MAAc,CAAC;UACnG,MAAME,sBAAsB,IAAAN,sBAAA,GAAGxJ,QAAQ,CACrCkB,aAAa,CAAC,sBAAsB,CAAC,cAAAsI,sBAAA,uBADRA,sBAAA,CAE5BG,QAAQ,CAAC9N,KAAK,CAAC+N,MAAc,CAAC;UACjC,MAAMG,iBAAiB,GAAIlO,KAAK,CAAC+N,MAAM,CAAiBzN,EAAE,CAAC6N,UAAU,CAAC,yBAAyB,CAAC;UAChG,MAAMC,WAAW,IAAAR,sBAAA,GAAGzJ,QAAQ,CAACkB,aAAa,CAAC,wBAAwB,CAAC,cAAAuI,sBAAA,uBAAhDA,sBAAA,CAAkDE,QAAQ,CAAC9N,KAAK,CAAC+N,MAAc,CAAC;UACpG;UACA,MAAMM,qBAAqB,GACzBrO,KAAK,CAAC+N,MAAM,CAAiBO,OAAO,CAAC,+BAA+B,CAAC,KAAK,IAAI;UAChF,MAAMC,YAAY,GACjBvO,KAAK,CAACH,IAAI,KAAK,OAAO,IACrBG,KAAK,CAACH,IAAI,KAAK,SAAS,IACvBG,KAAK,CAA8BwO,OAAO,IAC1CxO,KAAK,CAA8ByO,GAAG,KAAK,GAAI;UAClD;UACA,MAAMC,cAAc,GAAI1O,KAAK,CAAC+N,MAAM,CAAiBO,OAAO,CAAC,8BAA8B,CAAC,KAAK,IAAI;UACrG,MAAMK,WAAW,GAAGxK,QAAQ,CAACkB,aAAa,CAAC,gBAAgB,CAAC;UAE5D,IACC9G,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEmC,OAAO,IACf,CAACmN,aAAa,IACd,CAACK,iBAAiB,IAClB,CAACE,WAAW,IACZ,CAACC,qBAAqB,IACtB,CAACK,cAAc,IACf,CAACC,WAAW,IACZV,sBAAsB,KAAKtP,SAAS,EACnC;YACDJ,MAAM,CAACmC,OAAO,CAACE,SAAS,GAAG4M,UAAU;YACrCpR,qBAAqB,CAAC0C,mBAAmB,EAAE0O,UAAU,CAACzM,IAAI,CAAC,CAAC,CAAC;YAC7D1F,gBAAgB,CAAC,KAAK,CAAC;UACxB;QACD,CAAE;QACFuT,QAAQ,EAAGpB,UAAU,IAAK;UACzB,IAAIjP,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEmC,OAAO,EAAE;YACpBnC,MAAM,CAACmC,OAAO,CAACE,SAAS,GAAG4M,UAAU;UACtC;QACD;MAAE;QAAAzH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;IACR;IACA/B,QAAQ,CAAC0H,IAAI,EACb,oBACD;EAAC,gBACD,CAAC;AAEL,CAAC;AAACZ,GAAA,CAhQI7C,UAAU;AAAAyG,GAAA,GAAVzG,UAAU;AAiQhB,MAAMI,WAAW,GAAGA,CAAC;EAAE7K,KAAK;EAAE8K,UAAU;EAAEvK;AAA2E,CAAC,KAAK;EAC1H,oBACCtD,OAAA,CAACjB,aAAa;IACb+O,OAAO,EAAC,MAAM;IACd/K,KAAK,EAAEA,KAAM;IACbqH,QAAQ,EAAC,QAAQ;IACjByD,UAAU,EAAEA,UAAU,GAAG,CAAE;IAC3B7C,EAAE,EAAE;MAAEkJ,QAAQ,EAAE,CAAC;MAAEnJ,OAAO,EAAE,MAAM;MAAEoJ,cAAc,EAAE,QAAQ;MAAEC,UAAU,EAAC,SAAS;MAAE,+BAA+B,EAAE;QACpHpG,eAAe,EAAE1K,aAAa,CAAE;MAC/B;IAAC,CAAE;IACL+Q,UAAU,eAAErU,OAAA,CAAAE,SAAA,mBAAI,CAAE;IAClBoU,UAAU,eAAEtU,OAAA,CAAAE,SAAA,mBAAI;EAAE;IAAAiL,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClB,CAAC;AAEJ,CAAC;AAACiJ,GAAA,GAdI3G,WAAW;AAejB,MAAMK,iBAAiB,GAAGA,CAAC;EAAElL,KAAK;EAAE8K,UAAU;EAACvK;AAAwE,CAAC,KAAK;EAC5H,oBACCtD,OAAA,CAACxB,GAAG;IAACwM,EAAE,EAAE;MACRD,OAAO,EAAE,MAAM;MACfoJ,cAAc,EAAE,QAAQ;MACxB7E,GAAG,EAAE,KAAK,CAAE;IAEX,CAAE;IAAAzE,QAAA,EAGF2J,KAAK,CAACC,IAAI,CAAC;MAAE9G,MAAM,EAAE5K;IAAM,CAAC,CAAC,CAACoJ,GAAG,CAAC,CAACuI,CAAC,EAAE7N,KAAK,kBAC1C7G,OAAA;MAED8H,KAAK,EAAE;QACL5F,KAAK,EAAE,MAAM;QACb6F,MAAM,EAAE,KAAK;QACbiG,eAAe,EAAEnH,KAAK,KAAKgH,UAAU,GAAG,CAAC,GAAGvK,aAAa,GAAG,SAAS;QAAE;QACvEnB,YAAY,EAAE;MAChB;IAAE,GANG0E,KAAK;MAAAsE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAOR,CACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEE,CAAC;AAER,CAAC;AAACqJ,GAAA,GAxBI1G,iBAAiB;AAAA,IAAAC,EAAA,EAAAM,GAAA,EAAA4B,GAAA,EAAA6D,GAAA,EAAAM,GAAA,EAAAI,GAAA;AAAAC,YAAA,CAAA1G,EAAA;AAAA0G,YAAA,CAAApG,GAAA;AAAAoG,YAAA,CAAAxE,GAAA;AAAAwE,YAAA,CAAAX,GAAA;AAAAW,YAAA,CAAAL,GAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}