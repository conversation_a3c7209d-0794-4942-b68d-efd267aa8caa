[{"E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\index.tsx": "1", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\reportWebVitals.ts": "2", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\App.tsx": "3", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\UserInfoStore.ts": "4", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\Drawer.tsx": "5", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\AuthProvider.tsx": "6", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\AccountContext.tsx": "7", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\SnackbarContext.tsx": "8", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\drawerStore.ts": "9", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\historyStore.ts": "10", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\userSession.ts": "11", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuidePopUp.tsx": "12", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuideSettings.tsx": "13", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\LogoutPopup.tsx": "14", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\ExtensionLogin.tsx": "15", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\GuideMenuOptions.tsx": "16", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\ScrapingService.ts": "17", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\UserService.ts": "18", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\SaveGuideService.tsx": "19", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\GuideListServices.tsx": "20", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\assets\\icons\\icons.ts": "21", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\UseAuth.tsx": "22", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistLauncherPreview.tsx": "23", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPopup.tsx": "24", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\UndoRedoButtons.tsx": "25", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\TrainingField.tsx": "26", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Design.tsx": "27", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\Banners.tsx": "28", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Bannerspreview\\Banner.tsx": "29", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\AnnouncementPreview.tsx": "30", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\HotspotPreview.tsx": "31", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourTemplate.tsx": "32", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\Tooltip.tsx": "33", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\stepPopup.tsx": "34", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourPreview.tsx": "35", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltipuserview.tsx": "36", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\OrganizationService.ts": "37", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\FileService.tsx": "38", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\LoginService.tsx": "39", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\utils\\historyUtils.ts": "40", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementRules.tsx": "41", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PageTrigger.tsx": "42", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\AlertPopup.tsx": "43", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\StopScrapingButton.tsx": "44", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\PopupList.tsx": "45", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\OidcConfig.ts": "46", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Imagesection.tsx": "47", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\APIService.tsx": "48", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\RTEsection.tsx": "49", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\VideoSection.tsx": "50", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Button.tsx": "51", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPreview.tsx": "52", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\HtmlSection.tsx": "53", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\CreateWithAIButton.tsx": "54", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\ModernChatWindow.tsx": "55", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCheckIcon.tsx": "56", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\LauncherSettings.tsx": "57", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ImageCarousel.tsx": "58", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\VideoPlayer.tsx": "59", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\Chekpoints.tsx": "60", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CanvasSettings.tsx": "61", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Overlay.tsx": "62", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementsSettings.tsx": "63", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Animation.tsx": "64", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CustomCss.tsx": "65", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCanvasSettings.tsx": "66", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\TitleSubTitle.tsx": "67", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\PageInteraction.tsx": "68", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ButtonSettings.tsx": "69", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageSectionField.tsx": "70", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\hotspot\\HotspotSettings.tsx": "71", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\BannerStepPreview.tsx": "72", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\TooltipBody.tsx": "73", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltips.tsx": "74", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\designFields\\TooltipCanvasSettings.tsx": "75", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\CloneGuidePopUp.tsx": "76", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\TimeZoneConversion.tsx": "77", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\SelectImageFromApplication.tsx": "78", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\SpeechRecognitionService.ts": "79", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\AIService.ts": "80", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\DraggableCheckpoint.tsx": "81", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointEditPopup.tsx": "82", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointAddPopup.tsx": "83", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageProperties.tsx": "84", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageGalleryPopup.tsx": "85", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\Buttons.tsx": "86", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ImageSection.tsx": "87", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\RTE\\RTESection.tsx": "88", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ButtonSetting.tsx": "89", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\i18n.ts": "90", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\contexts\\TranslationContext.tsx": "91", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\LanguageService.ts": "92", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\LanguageSelector.tsx": "93"}, {"size": 604, "mtime": 1748929949904, "results": "94", "hashOfConfig": "95"}, {"size": 440, "mtime": 1748929949904, "results": "96", "hashOfConfig": "95"}, {"size": 2367, "mtime": 1752757647829, "results": "97", "hashOfConfig": "95"}, {"size": 3050, "mtime": 1752571609568, "results": "98", "hashOfConfig": "95"}, {"size": 240421, "mtime": 1752757497891, "results": "99", "hashOfConfig": "95"}, {"size": 6057, "mtime": 1751532053862, "results": "100", "hashOfConfig": "95"}, {"size": 636, "mtime": 1748929949889, "results": "101", "hashOfConfig": "95"}, {"size": 3112, "mtime": 1752571609553, "results": "102", "hashOfConfig": "95"}, {"size": 392115, "mtime": 1752757801237, "results": "103", "hashOfConfig": "95"}, {"size": 3927, "mtime": 1748930023076, "results": "104", "hashOfConfig": "95"}, {"size": 6144, "mtime": 1749531263982, "results": "105", "hashOfConfig": "95"}, {"size": 40511, "mtime": 1752728294203, "results": "106", "hashOfConfig": "95"}, {"size": 5077, "mtime": 1752728294207, "results": "107", "hashOfConfig": "95"}, {"size": 3731, "mtime": 1752571609521, "results": "108", "hashOfConfig": "95"}, {"size": 12723, "mtime": 1752571609568, "results": "109", "hashOfConfig": "95"}, {"size": 13085, "mtime": 1752728294234, "results": "110", "hashOfConfig": "95"}, {"size": 26975, "mtime": 1748930023061, "results": "111", "hashOfConfig": "95"}, {"size": 1898, "mtime": 1748930023076, "results": "112", "hashOfConfig": "95"}, {"size": 1954, "mtime": 1751432283612, "results": "113", "hashOfConfig": "95"}, {"size": 8101, "mtime": 1752571609568, "results": "114", "hashOfConfig": "95"}, {"size": 296570, "mtime": 1752571609475, "results": "115", "hashOfConfig": "95"}, {"size": 193, "mtime": 1748929949654, "results": "116", "hashOfConfig": "95"}, {"size": 9064, "mtime": 1752571609490, "results": "117", "hashOfConfig": "95"}, {"size": 30624, "mtime": 1752571609506, "results": "118", "hashOfConfig": "95"}, {"size": 3012, "mtime": 1752728294177, "results": "119", "hashOfConfig": "95"}, {"size": 2448, "mtime": 1748930022545, "results": "120", "hashOfConfig": "95"}, {"size": 33372, "mtime": 1752728294198, "results": "121", "hashOfConfig": "95"}, {"size": 23258, "mtime": 1752728294195, "results": "122", "hashOfConfig": "95"}, {"size": 13556, "mtime": 1752571609475, "results": "123", "hashOfConfig": "95"}, {"size": 26704, "mtime": 1752728294134, "results": "124", "hashOfConfig": "95"}, {"size": 49705, "mtime": 1751532053846, "results": "125", "hashOfConfig": "95"}, {"size": 7599, "mtime": 1752571609568, "results": "126", "hashOfConfig": "95"}, {"size": 30050, "mtime": 1752757801221, "results": "127", "hashOfConfig": "95"}, {"size": 11310, "mtime": 1752571609568, "results": "128", "hashOfConfig": "95"}, {"size": 24200, "mtime": 1751432283612, "results": "129", "hashOfConfig": "95"}, {"size": 4880, "mtime": 1750229130169, "results": "130", "hashOfConfig": "95"}, {"size": 9238, "mtime": 1748930023061, "results": "131", "hashOfConfig": "95"}, {"size": 1297, "mtime": 1748930023061, "results": "132", "hashOfConfig": "95"}, {"size": 1248, "mtime": 1748929949920, "results": "133", "hashOfConfig": "95"}, {"size": 14238, "mtime": 1748930023076, "results": "134", "hashOfConfig": "95"}, {"size": 2997, "mtime": 1752571609537, "results": "135", "hashOfConfig": "95"}, {"size": 3285, "mtime": 1752728294211, "results": "136", "hashOfConfig": "95"}, {"size": 2750, "mtime": 1752757497860, "results": "137", "hashOfConfig": "95"}, {"size": 850, "mtime": 1748930022545, "results": "138", "hashOfConfig": "95"}, {"size": 19401, "mtime": 1752728294238, "results": "139", "hashOfConfig": "95"}, {"size": 743, "mtime": 1748929949654, "results": "140", "hashOfConfig": "95"}, {"size": 24904, "mtime": 1752728294221, "results": "141", "hashOfConfig": "95"}, {"size": 2608, "mtime": 1748930023061, "results": "142", "hashOfConfig": "95"}, {"size": 29917, "mtime": 1752813281888, "results": "143", "hashOfConfig": "95"}, {"size": 7772, "mtime": 1752571609553, "results": "144", "hashOfConfig": "95"}, {"size": 16105, "mtime": 1752757497891, "results": "145", "hashOfConfig": "95"}, {"size": 29082, "mtime": 1752571609506, "results": "146", "hashOfConfig": "95"}, {"size": 6245, "mtime": 1748929949857, "results": "147", "hashOfConfig": "95"}, {"size": 1639, "mtime": 1752571609475, "results": "148", "hashOfConfig": "95"}, {"size": 29616, "mtime": 1752571609475, "results": "149", "hashOfConfig": "95"}, {"size": 1962, "mtime": 1748929949654, "results": "150", "hashOfConfig": "95"}, {"size": 27206, "mtime": 1752728294171, "results": "151", "hashOfConfig": "95"}, {"size": 2401, "mtime": 1752571609506, "results": "152", "hashOfConfig": "95"}, {"size": 702, "mtime": 1752571609506, "results": "153", "hashOfConfig": "95"}, {"size": 13889, "mtime": 1752571609506, "results": "154", "hashOfConfig": "95"}, {"size": 19040, "mtime": 1752571609537, "results": "155", "hashOfConfig": "95"}, {"size": 6625, "mtime": 1752571609537, "results": "156", "hashOfConfig": "95"}, {"size": 20321, "mtime": 1752738005916, "results": "157", "hashOfConfig": "95"}, {"size": 3236, "mtime": 1748929949779, "results": "158", "hashOfConfig": "95"}, {"size": 2848, "mtime": 1748929949811, "results": "159", "hashOfConfig": "95"}, {"size": 15285, "mtime": 1752571609490, "results": "160", "hashOfConfig": "95"}, {"size": 15217, "mtime": 1752571609506, "results": "161", "hashOfConfig": "95"}, {"size": 11208, "mtime": 1752571609537, "results": "162", "hashOfConfig": "95"}, {"size": 16370, "mtime": 1752571609537, "results": "163", "hashOfConfig": "95"}, {"size": 8476, "mtime": 1752571609537, "results": "164", "hashOfConfig": "95"}, {"size": 15571, "mtime": 1752571609568, "results": "165", "hashOfConfig": "95"}, {"size": 16126, "mtime": 1749557445376, "results": "166", "hashOfConfig": "95"}, {"size": 33320, "mtime": 1752765057793, "results": "167", "hashOfConfig": "95"}, {"size": 60407, "mtime": 1752571609475, "results": "168", "hashOfConfig": "95"}, {"size": 26698, "mtime": 1752571609490, "results": "169", "hashOfConfig": "95"}, {"size": 5258, "mtime": 1752571609553, "results": "170", "hashOfConfig": "95"}, {"size": 883, "mtime": 1748929949889, "results": "171", "hashOfConfig": "95"}, {"size": 2196, "mtime": 1752571609521, "results": "172", "hashOfConfig": "95"}, {"size": 7943, "mtime": 1748930023061, "results": "173", "hashOfConfig": "95"}, {"size": 491, "mtime": 1751432283612, "results": "174", "hashOfConfig": "95"}, {"size": 5504, "mtime": 1752571609506, "results": "175", "hashOfConfig": "95"}, {"size": 32764, "mtime": 1752728294162, "results": "176", "hashOfConfig": "95"}, {"size": 37265, "mtime": 1752728294157, "results": "177", "hashOfConfig": "95"}, {"size": 2931, "mtime": 1749010760558, "results": "178", "hashOfConfig": "95"}, {"size": 2669, "mtime": 1748929949748, "results": "179", "hashOfConfig": "95"}, {"size": 17317, "mtime": 1752757497860, "results": "180", "hashOfConfig": "95"}, {"size": 27631, "mtime": 1752728294148, "results": "181", "hashOfConfig": "95"}, {"size": 13227, "mtime": 1752813311248, "results": "182", "hashOfConfig": "95"}, {"size": 14592, "mtime": 1752571609490, "results": "183", "hashOfConfig": "95"}, {"size": 7289, "mtime": 1752571609568, "results": "184", "hashOfConfig": "95"}, {"size": 5493, "mtime": 1752571609568, "results": "185", "hashOfConfig": "95"}, {"size": 876, "mtime": 1752571609568, "results": "186", "hashOfConfig": "95"}, {"size": 5767, "mtime": 1752728294174, "results": "187", "hashOfConfig": "95"}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1c51j82", {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 220, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 40, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 61, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 25, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 48, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 54, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 57, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 33, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 31, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 28, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 63, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 42, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 29, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 49, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 62, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 33, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\index.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\reportWebVitals.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\App.tsx", ["467", "468"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\UserInfoStore.ts", ["469"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\Drawer.tsx", ["470", "471", "472", "473", "474", "475", "476", "477", "478", "479", "480", "481", "482", "483", "484", "485", "486", "487", "488", "489", "490", "491", "492", "493", "494", "495", "496", "497", "498", "499", "500", "501", "502", "503", "504", "505", "506", "507", "508", "509", "510", "511", "512", "513", "514", "515", "516", "517", "518", "519", "520", "521", "522", "523", "524", "525", "526", "527", "528", "529", "530", "531", "532", "533", "534", "535", "536", "537", "538", "539", "540", "541", "542", "543", "544", "545", "546", "547", "548", "549", "550", "551", "552", "553", "554", "555", "556", "557", "558", "559", "560", "561", "562", "563", "564", "565", "566", "567", "568", "569", "570", "571", "572", "573", "574", "575", "576", "577", "578", "579", "580", "581", "582", "583", "584", "585", "586", "587", "588", "589", "590", "591", "592", "593", "594", "595", "596", "597", "598", "599", "600", "601", "602", "603", "604", "605", "606", "607", "608", "609", "610", "611", "612", "613", "614", "615", "616", "617", "618", "619", "620", "621", "622", "623", "624", "625", "626", "627", "628", "629", "630", "631", "632", "633", "634", "635", "636", "637", "638", "639", "640", "641", "642", "643", "644", "645", "646", "647", "648", "649", "650", "651", "652", "653", "654", "655", "656", "657", "658", "659", "660", "661", "662", "663", "664", "665", "666", "667", "668", "669", "670", "671", "672", "673", "674", "675", "676", "677", "678", "679", "680", "681", "682", "683", "684", "685", "686", "687", "688", "689"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\AuthProvider.tsx", ["690", "691", "692", "693", "694", "695"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\AccountContext.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\SnackbarContext.tsx", ["696", "697"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\drawerStore.ts", ["698", "699", "700", "701", "702", "703", "704", "705", "706", "707", "708", "709", "710", "711", "712", "713", "714", "715", "716", "717"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\historyStore.ts", ["718"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\userSession.ts", ["719"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuidePopUp.tsx", ["720", "721", "722", "723", "724", "725", "726", "727", "728", "729", "730", "731", "732", "733", "734", "735", "736", "737", "738", "739", "740", "741", "742", "743", "744", "745", "746", "747", "748", "749", "750", "751", "752", "753", "754", "755", "756", "757", "758", "759"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuideSettings.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\LogoutPopup.tsx", ["760"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\ExtensionLogin.tsx", ["761", "762", "763", "764", "765", "766", "767", "768", "769", "770", "771", "772", "773"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\GuideMenuOptions.tsx", ["774", "775", "776", "777", "778", "779", "780", "781", "782", "783", "784", "785", "786", "787", "788", "789", "790", "791", "792", "793", "794", "795", "796", "797"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\ScrapingService.ts", ["798", "799", "800", "801"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\UserService.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\SaveGuideService.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\GuideListServices.tsx", ["802", "803", "804"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\assets\\icons\\icons.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\UseAuth.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistLauncherPreview.tsx", ["805", "806", "807", "808", "809", "810", "811", "812"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPopup.tsx", ["813", "814", "815", "816", "817", "818", "819", "820", "821", "822", "823", "824", "825", "826", "827", "828", "829", "830", "831", "832", "833", "834", "835", "836", "837", "838", "839"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\UndoRedoButtons.tsx", ["840", "841", "842", "843"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\TrainingField.tsx", ["844", "845", "846", "847", "848", "849", "850", "851", "852", "853", "854", "855", "856", "857", "858", "859"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Design.tsx", ["860", "861", "862", "863", "864", "865", "866", "867", "868", "869", "870", "871", "872", "873", "874", "875", "876", "877", "878", "879", "880", "881", "882", "883", "884", "885", "886", "887", "888", "889", "890", "891", "892", "893", "894", "895", "896", "897", "898", "899", "900", "901", "902", "903", "904", "905", "906", "907", "908", "909", "910", "911", "912", "913", "914", "915", "916", "917", "918", "919", "920"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\Banners.tsx", ["921", "922", "923", "924", "925", "926", "927", "928", "929", "930", "931", "932", "933", "934", "935", "936", "937", "938", "939", "940", "941", "942", "943"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Bannerspreview\\Banner.tsx", ["944", "945", "946", "947", "948", "949", "950", "951", "952", "953", "954", "955", "956", "957", "958", "959", "960", "961", "962", "963", "964"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\AnnouncementPreview.tsx", ["965", "966", "967", "968", "969", "970", "971", "972", "973", "974", "975", "976", "977", "978", "979", "980", "981", "982", "983", "984", "985", "986", "987", "988", "989"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\HotspotPreview.tsx", ["990", "991", "992", "993", "994", "995", "996", "997", "998", "999", "1000", "1001", "1002", "1003", "1004", "1005", "1006", "1007", "1008", "1009", "1010", "1011", "1012", "1013", "1014", "1015", "1016"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourTemplate.tsx", ["1017", "1018", "1019", "1020", "1021", "1022", "1023", "1024", "1025", "1026"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\Tooltip.tsx", ["1027", "1028", "1029", "1030", "1031", "1032", "1033", "1034", "1035", "1036", "1037", "1038", "1039", "1040", "1041", "1042", "1043", "1044", "1045", "1046", "1047", "1048", "1049", "1050", "1051", "1052", "1053", "1054", "1055", "1056", "1057", "1058", "1059", "1060", "1061", "1062", "1063", "1064", "1065", "1066", "1067", "1068", "1069", "1070", "1071", "1072", "1073", "1074"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\stepPopup.tsx", ["1075", "1076", "1077", "1078", "1079", "1080", "1081", "1082", "1083"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourPreview.tsx", ["1084", "1085", "1086", "1087", "1088", "1089", "1090", "1091", "1092", "1093", "1094", "1095", "1096", "1097", "1098", "1099", "1100", "1101", "1102", "1103", "1104"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltipuserview.tsx", ["1105", "1106", "1107", "1108", "1109", "1110"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\OrganizationService.ts", ["1111", "1112", "1113", "1114"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\FileService.tsx", ["1115", "1116"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\LoginService.tsx", ["1117", "1118", "1119"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\utils\\historyUtils.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementRules.tsx", ["1120", "1121"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PageTrigger.tsx", ["1122"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\AlertPopup.tsx", ["1123", "1124", "1125"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\StopScrapingButton.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\PopupList.tsx", ["1126", "1127", "1128", "1129", "1130", "1131", "1132", "1133", "1134", "1135"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\OidcConfig.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Imagesection.tsx", ["1136", "1137", "1138"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\APIService.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\RTEsection.tsx", ["1139", "1140", "1141", "1142"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\VideoSection.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Button.tsx", ["1143", "1144", "1145", "1146", "1147", "1148", "1149", "1150", "1151", "1152", "1153", "1154", "1155", "1156", "1157", "1158", "1159", "1160", "1161", "1162", "1163", "1164", "1165", "1166", "1167", "1168", "1169"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPreview.tsx", ["1170", "1171", "1172", "1173", "1174", "1175", "1176", "1177", "1178", "1179", "1180"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\HtmlSection.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\CreateWithAIButton.tsx", ["1181", "1182", "1183"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\ModernChatWindow.tsx", ["1184", "1185", "1186", "1187", "1188", "1189", "1190", "1191", "1192", "1193", "1194", "1195", "1196", "1197"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCheckIcon.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\LauncherSettings.tsx", ["1198", "1199", "1200", "1201", "1202", "1203", "1204", "1205", "1206", "1207", "1208", "1209", "1210", "1211", "1212", "1213", "1214", "1215", "1216", "1217", "1218", "1219", "1220", "1221", "1222", "1223", "1224", "1225", "1226", "1227", "1228", "1229", "1230", "1231", "1232", "1233", "1234", "1235", "1236", "1237", "1238", "1239", "1240", "1241", "1242", "1243", "1244", "1245", "1246", "1247", "1248", "1249", "1250", "1251"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ImageCarousel.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\VideoPlayer.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\Chekpoints.tsx", ["1252", "1253", "1254", "1255", "1256", "1257", "1258", "1259", "1260", "1261", "1262", "1263", "1264", "1265", "1266", "1267", "1268", "1269", "1270", "1271", "1272", "1273", "1274", "1275", "1276", "1277", "1278", "1279", "1280", "1281", "1282", "1283", "1284", "1285", "1286", "1287", "1288", "1289", "1290", "1291", "1292", "1293", "1294", "1295", "1296", "1297", "1298", "1299", "1300", "1301", "1302", "1303", "1304", "1305", "1306", "1307", "1308"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CanvasSettings.tsx", ["1309", "1310", "1311", "1312"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Overlay.tsx", ["1313", "1314", "1315", "1316", "1317", "1318"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementsSettings.tsx", ["1319", "1320", "1321", "1322", "1323", "1324", "1325", "1326", "1327", "1328", "1329", "1330", "1331", "1332", "1333", "1334", "1335", "1336", "1337", "1338"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Animation.tsx", ["1339", "1340", "1341"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CustomCss.tsx", ["1342", "1343"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCanvasSettings.tsx", ["1344", "1345", "1346", "1347", "1348", "1349", "1350", "1351", "1352", "1353", "1354", "1355", "1356", "1357", "1358", "1359", "1360", "1361", "1362", "1363", "1364", "1365", "1366", "1367", "1368", "1369", "1370", "1371", "1372", "1373", "1374", "1375", "1376"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\TitleSubTitle.tsx", ["1377", "1378", "1379", "1380", "1381", "1382", "1383", "1384", "1385", "1386", "1387", "1388", "1389", "1390", "1391", "1392", "1393", "1394", "1395", "1396", "1397", "1398", "1399", "1400", "1401", "1402", "1403", "1404", "1405", "1406", "1407"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\PageInteraction.tsx", ["1408", "1409", "1410", "1411", "1412", "1413", "1414", "1415", "1416", "1417", "1418", "1419", "1420", "1421", "1422", "1423", "1424", "1425", "1426", "1427", "1428", "1429", "1430", "1431", "1432", "1433", "1434", "1435"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ButtonSettings.tsx", ["1436", "1437", "1438", "1439", "1440", "1441", "1442", "1443", "1444", "1445", "1446", "1447", "1448", "1449", "1450", "1451", "1452", "1453", "1454", "1455", "1456", "1457", "1458", "1459", "1460", "1461", "1462", "1463", "1464", "1465", "1466", "1467", "1468", "1469", "1470", "1471", "1472", "1473", "1474", "1475", "1476", "1477", "1478", "1479", "1480", "1481", "1482", "1483", "1484", "1485", "1486", "1487", "1488", "1489", "1490", "1491", "1492", "1493", "1494", "1495", "1496", "1497", "1498"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageSectionField.tsx", ["1499", "1500", "1501", "1502", "1503", "1504", "1505", "1506"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\hotspot\\HotspotSettings.tsx", ["1507", "1508", "1509", "1510", "1511", "1512", "1513", "1514", "1515", "1516", "1517", "1518", "1519", "1520", "1521", "1522", "1523", "1524", "1525"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\BannerStepPreview.tsx", ["1526", "1527", "1528", "1529", "1530", "1531", "1532", "1533", "1534", "1535", "1536", "1537", "1538", "1539", "1540", "1541", "1542", "1543", "1544", "1545"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\TooltipBody.tsx", ["1546", "1547", "1548", "1549", "1550", "1551", "1552", "1553", "1554", "1555", "1556", "1557", "1558", "1559", "1560", "1561", "1562", "1563", "1564", "1565", "1566", "1567", "1568", "1569", "1570", "1571", "1572", "1573", "1574", "1575", "1576", "1577", "1578", "1579", "1580", "1581", "1582", "1583", "1584", "1585", "1586", "1587"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltips.tsx", ["1588", "1589", "1590", "1591", "1592", "1593", "1594", "1595", "1596", "1597", "1598", "1599", "1600", "1601", "1602", "1603"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\designFields\\TooltipCanvasSettings.tsx", ["1604", "1605", "1606", "1607", "1608", "1609", "1610", "1611", "1612", "1613", "1614", "1615", "1616", "1617", "1618", "1619", "1620"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\CloneGuidePopUp.tsx", ["1621", "1622"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\TimeZoneConversion.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\SelectImageFromApplication.tsx", ["1623", "1624", "1625", "1626", "1627", "1628", "1629", "1630", "1631", "1632"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\SpeechRecognitionService.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\AIService.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\DraggableCheckpoint.tsx", ["1633", "1634", "1635", "1636", "1637", "1638", "1639", "1640", "1641", "1642", "1643", "1644", "1645", "1646", "1647", "1648", "1649", "1650", "1651", "1652", "1653", "1654", "1655", "1656", "1657", "1658", "1659", "1660", "1661"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointEditPopup.tsx", ["1662", "1663", "1664", "1665", "1666", "1667", "1668", "1669", "1670", "1671", "1672", "1673", "1674", "1675", "1676", "1677", "1678", "1679", "1680", "1681", "1682", "1683", "1684", "1685", "1686", "1687", "1688", "1689", "1690", "1691", "1692", "1693", "1694", "1695", "1696", "1697", "1698", "1699", "1700", "1701", "1702", "1703", "1704", "1705", "1706", "1707", "1708", "1709", "1710"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointAddPopup.tsx", ["1711", "1712", "1713", "1714", "1715", "1716", "1717", "1718", "1719", "1720", "1721", "1722", "1723", "1724", "1725", "1726", "1727", "1728", "1729", "1730", "1731", "1732", "1733", "1734", "1735", "1736", "1737", "1738", "1739", "1740", "1741", "1742", "1743", "1744", "1745", "1746", "1747", "1748", "1749", "1750", "1751", "1752", "1753", "1754", "1755", "1756", "1757", "1758", "1759", "1760", "1761", "1762", "1763", "1764", "1765", "1766", "1767", "1768", "1769", "1770", "1771", "1772"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageProperties.tsx", ["1773", "1774", "1775", "1776", "1777", "1778", "1779", "1780", "1781", "1782", "1783", "1784", "1785", "1786"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageGalleryPopup.tsx", ["1787", "1788"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\Buttons.tsx", ["1789", "1790", "1791", "1792", "1793", "1794", "1795", "1796", "1797", "1798"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ImageSection.tsx", ["1799", "1800", "1801", "1802", "1803", "1804", "1805", "1806", "1807", "1808", "1809", "1810", "1811"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\RTE\\RTESection.tsx", ["1812", "1813", "1814", "1815", "1816", "1817", "1818", "1819", "1820", "1821", "1822", "1823", "1824", "1825", "1826", "1827", "1828", "1829", "1830", "1831", "1832", "1833", "1834", "1835", "1836", "1837", "1838", "1839", "1840", "1841", "1842", "1843", "1844"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ButtonSetting.tsx", ["1845", "1846", "1847", "1848", "1849", "1850", "1851", "1852", "1853", "1854", "1855", "1856", "1857"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\i18n.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\contexts\\TranslationContext.tsx", ["1858"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\LanguageService.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\LanguageSelector.tsx", ["1859", "1860"], [], {"ruleId": "1861", "severity": 1, "message": "1862", "line": 3, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 3, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "1865", "line": 9, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 9, "endColumn": 11}, {"ruleId": "1861", "severity": 1, "message": "1866", "line": 1, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "1867", "line": 1, "column": 58, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 65}, {"ruleId": "1861", "severity": 1, "message": "1868", "line": 5, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 5, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "1869", "line": 6, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 6, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "1870", "line": 7, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 7, "endColumn": 28}, {"ruleId": "1861", "severity": 1, "message": "1871", "line": 12, "column": 28, "nodeType": "1863", "messageId": "1864", "endLine": 12, "endColumn": 40}, {"ruleId": "1861", "severity": 1, "message": "1872", "line": 17, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 17, "endColumn": 9}, {"ruleId": "1861", "severity": 1, "message": "1873", "line": 22, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 22, "endColumn": 10}, {"ruleId": "1861", "severity": 1, "message": "1874", "line": 23, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 23, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "1875", "line": 24, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 24, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "1876", "line": 25, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 25, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "1877", "line": 26, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 26, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "1878", "line": 27, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 27, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "1879", "line": 28, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 28, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "1880", "line": 29, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 29, "endColumn": 12}, {"ruleId": "1861", "severity": 1, "message": "1881", "line": 30, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 30, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "1882", "line": 31, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 31, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "1883", "line": 32, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 32, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "1884", "line": 33, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 33, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "1885", "line": 34, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 34, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "1886", "line": 35, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 35, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "1887", "line": 37, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 37, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "1888", "line": 38, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 38, "endColumn": 10}, {"ruleId": "1861", "severity": 1, "message": "1889", "line": 39, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 39, "endColumn": 10}, {"ruleId": "1861", "severity": 1, "message": "1890", "line": 40, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 40, "endColumn": 11}, {"ruleId": "1861", "severity": 1, "message": "1891", "line": 44, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 44, "endColumn": 10}, {"ruleId": "1861", "severity": 1, "message": "1892", "line": 50, "column": 20, "nodeType": "1863", "messageId": "1864", "endLine": 50, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "1893", "line": 60, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 60, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "1894", "line": 61, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 61, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "1895", "line": 68, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 68, "endColumn": 6}, {"ruleId": "1861", "severity": 1, "message": "1896", "line": 69, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 69, "endColumn": 6}, {"ruleId": "1861", "severity": 1, "message": "1897", "line": 76, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 76, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "1898", "line": 78, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 78, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "1899", "line": 79, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 79, "endColumn": 28}, {"ruleId": "1861", "severity": 1, "message": "1900", "line": 79, "column": 30, "nodeType": "1863", "messageId": "1864", "endLine": 79, "endColumn": 39}, {"ruleId": "1861", "severity": 1, "message": "1901", "line": 82, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 82, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "1902", "line": 83, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 83, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "1903", "line": 84, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 84, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "1904", "line": 88, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 88, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "1905", "line": 90, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 90, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "1906", "line": 95, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 95, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "1907", "line": 102, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 102, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "1908", "line": 105, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 105, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "1909", "line": 106, "column": 26, "nodeType": "1863", "messageId": "1864", "endLine": 106, "endColumn": 37}, {"ruleId": "1861", "severity": 1, "message": "1910", "line": 111, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 111, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "1911", "line": 111, "column": 21, "nodeType": "1863", "messageId": "1864", "endLine": 111, "endColumn": 28}, {"ruleId": "1861", "severity": 1, "message": "1912", "line": 114, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 114, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "1913", "line": 121, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 121, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "1914", "line": 134, "column": 11, "nodeType": "1863", "messageId": "1864", "endLine": 134, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "1915", "line": 197, "column": 11, "nodeType": "1863", "messageId": "1864", "endLine": 197, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "1916", "line": 214, "column": 11, "nodeType": "1863", "messageId": "1864", "endLine": 214, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "1917", "line": 222, "column": 11, "nodeType": "1863", "messageId": "1864", "endLine": 222, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "1918", "line": 378, "column": 11, "nodeType": "1863", "messageId": "1864", "endLine": 378, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "1919", "line": 413, "column": 5, "nodeType": "1863", "messageId": "1864", "endLine": 413, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "1920", "line": 415, "column": 6, "nodeType": "1863", "messageId": "1864", "endLine": 415, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "1921", "line": 417, "column": 6, "nodeType": "1863", "messageId": "1864", "endLine": 417, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "1922", "line": 432, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 432, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "1923", "line": 433, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 433, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "1924", "line": 435, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 435, "endColumn": 28}, {"ruleId": "1861", "severity": 1, "message": "1925", "line": 438, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 438, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "1926", "line": 442, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 442, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "1927", "line": 443, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 443, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "1928", "line": 454, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 454, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "1929", "line": 455, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 455, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "1930", "line": 456, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 456, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "1931", "line": 458, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 458, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "1932", "line": 458, "column": 26, "nodeType": "1863", "messageId": "1864", "endLine": 458, "endColumn": 44}, {"ruleId": "1861", "severity": 1, "message": "1933", "line": 463, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 463, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "1934", "line": 463, "column": 23, "nodeType": "1863", "messageId": "1864", "endLine": 463, "endColumn": 38}, {"ruleId": "1861", "severity": 1, "message": "1935", "line": 465, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 465, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "1936", "line": 465, "column": 19, "nodeType": "1863", "messageId": "1864", "endLine": 465, "endColumn": 30}, {"ruleId": "1861", "severity": 1, "message": "1937", "line": 468, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 468, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "1938", "line": 468, "column": 24, "nodeType": "1863", "messageId": "1864", "endLine": 468, "endColumn": 40}, {"ruleId": "1861", "severity": 1, "message": "1939", "line": 469, "column": 19, "nodeType": "1863", "messageId": "1864", "endLine": 469, "endColumn": 30}, {"ruleId": "1861", "severity": 1, "message": "1940", "line": 474, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 474, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "1941", "line": 474, "column": 29, "nodeType": "1863", "messageId": "1864", "endLine": 474, "endColumn": 50}, {"ruleId": "1861", "severity": 1, "message": "1942", "line": 481, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 481, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "1943", "line": 481, "column": 16, "nodeType": "1863", "messageId": "1864", "endLine": 481, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "1944", "line": 483, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 483, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "1945", "line": 483, "column": 27, "nodeType": "1863", "messageId": "1864", "endLine": 483, "endColumn": 41}, {"ruleId": "1861", "severity": 1, "message": "1946", "line": 485, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 485, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "1947", "line": 485, "column": 16, "nodeType": "1863", "messageId": "1864", "endLine": 485, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "1948", "line": 498, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 498, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "1949", "line": 499, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 499, "endColumn": 31}, {"ruleId": "1861", "severity": 1, "message": "1950", "line": 499, "column": 33, "nodeType": "1863", "messageId": "1864", "endLine": 499, "endColumn": 58}, {"ruleId": "1861", "severity": 1, "message": "1951", "line": 502, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 502, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "1952", "line": 502, "column": 19, "nodeType": "1863", "messageId": "1864", "endLine": 502, "endColumn": 30}, {"ruleId": "1861", "severity": 1, "message": "1953", "line": 503, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 503, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "1954", "line": 503, "column": 17, "nodeType": "1863", "messageId": "1864", "endLine": 503, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "1955", "line": 504, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 504, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "1956", "line": 504, "column": 21, "nodeType": "1863", "messageId": "1864", "endLine": 504, "endColumn": 34}, {"ruleId": "1861", "severity": 1, "message": "1957", "line": 513, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 513, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "1958", "line": 514, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 514, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "1959", "line": 520, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 520, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "1960", "line": 524, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 524, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "1961", "line": 524, "column": 26, "nodeType": "1863", "messageId": "1864", "endLine": 524, "endColumn": 44}, {"ruleId": "1861", "severity": 1, "message": "1962", "line": 527, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 527, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "1963", "line": 527, "column": 20, "nodeType": "1863", "messageId": "1864", "endLine": 527, "endColumn": 32}, {"ruleId": "1964", "severity": 1, "message": "1965", "line": 566, "column": 5, "nodeType": "1966", "endLine": 566, "endColumn": 27, "suggestions": "1967"}, {"ruleId": "1861", "severity": 1, "message": "1968", "line": 576, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 576, "endColumn": 6}, {"ruleId": "1861", "severity": 1, "message": "1969", "line": 577, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 577, "endColumn": 7}, {"ruleId": "1861", "severity": 1, "message": "1970", "line": 578, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 578, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "1971", "line": 580, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 580, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "1972", "line": 581, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 581, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "1973", "line": 586, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 586, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "1974", "line": 587, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 587, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "1975", "line": 622, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 622, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "1976", "line": 623, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 623, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "1977", "line": 624, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 624, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "1978", "line": 632, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 632, "endColumn": 10}, {"ruleId": "1861", "severity": 1, "message": "1979", "line": 634, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 634, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "1980", "line": 635, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 635, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "1981", "line": 636, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 636, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "1982", "line": 637, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 637, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "1983", "line": 642, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 642, "endColumn": 11}, {"ruleId": "1861", "severity": 1, "message": "1984", "line": 644, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 644, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "1985", "line": 646, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 646, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "1986", "line": 656, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 656, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "1987", "line": 657, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 657, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "1988", "line": 660, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 660, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "1989", "line": 664, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 664, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "1990", "line": 666, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 666, "endColumn": 28}, {"ruleId": "1861", "severity": 1, "message": "1991", "line": 667, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 667, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "1992", "line": 669, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 669, "endColumn": 11}, {"ruleId": "1861", "severity": 1, "message": "1993", "line": 676, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 676, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "1994", "line": 677, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 677, "endColumn": 12}, {"ruleId": "1861", "severity": 1, "message": "1995", "line": 682, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 682, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "1996", "line": 683, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 683, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "1997", "line": 684, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 684, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "1998", "line": 694, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 694, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "1999", "line": 698, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 698, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2000", "line": 702, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 702, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2001", "line": 704, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 704, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2002", "line": 706, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 706, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2003", "line": 707, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 707, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2004", "line": 712, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 712, "endColumn": 12}, {"ruleId": "1861", "severity": 1, "message": "2005", "line": 713, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 713, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2006", "line": 724, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 724, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2007", "line": 730, "column": 18, "nodeType": "1863", "messageId": "1864", "endLine": 730, "endColumn": 37}, {"ruleId": "1861", "severity": 1, "message": "2008", "line": 731, "column": 18, "nodeType": "1863", "messageId": "1864", "endLine": 731, "endColumn": 37}, {"ruleId": "1861", "severity": 1, "message": "2009", "line": 735, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 735, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2010", "line": 747, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 747, "endColumn": 35}, {"ruleId": "1861", "severity": 1, "message": "2011", "line": 772, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 772, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2012", "line": 783, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 783, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2013", "line": 788, "column": 25, "nodeType": "1863", "messageId": "1864", "endLine": 788, "endColumn": 42}, {"ruleId": "2014", "severity": 1, "message": "2015", "line": 793, "column": 22, "nodeType": "2016", "messageId": "2017", "endLine": 793, "endColumn": 24}, {"ruleId": "1964", "severity": 1, "message": "2018", "line": 879, "column": 5, "nodeType": "1966", "endLine": 879, "endColumn": 46, "suggestions": "2019"}, {"ruleId": "1964", "severity": 1, "message": "2020", "line": 879, "column": 6, "nodeType": "2021", "endLine": 879, "endColumn": 29}, {"ruleId": "1964", "severity": 1, "message": "2022", "line": 897, "column": 5, "nodeType": "1966", "endLine": 897, "endColumn": 18, "suggestions": "2023"}, {"ruleId": "1861", "severity": 1, "message": "2024", "line": 899, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 899, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2025", "line": 900, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 900, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2026", "line": 921, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 921, "endColumn": 24}, {"ruleId": "1964", "severity": 1, "message": "2027", "line": 945, "column": 8, "nodeType": "2028", "endLine": 947, "endColumn": 3}, {"ruleId": "1964", "severity": 1, "message": "2029", "line": 982, "column": 5, "nodeType": "1966", "endLine": 990, "endColumn": 3, "suggestions": "2030"}, {"ruleId": "1964", "severity": 1, "message": "2031", "line": 1018, "column": 5, "nodeType": "1966", "endLine": 1041, "endColumn": 3, "suggestions": "2032"}, {"ruleId": "1964", "severity": 1, "message": "2033", "line": 1159, "column": 5, "nodeType": "1966", "endLine": 1159, "endColumn": 39, "suggestions": "2034"}, {"ruleId": "1861", "severity": 1, "message": "2035", "line": 1276, "column": 16, "nodeType": "1863", "messageId": "1864", "endLine": 1276, "endColumn": 24}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 1367, "column": 25, "nodeType": "2016", "messageId": "2017", "endLine": 1367, "endColumn": 27}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 1374, "column": 25, "nodeType": "2016", "messageId": "2017", "endLine": 1374, "endColumn": 27}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 1374, "column": 53, "nodeType": "2016", "messageId": "2017", "endLine": 1374, "endColumn": 55}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 1377, "column": 26, "nodeType": "2016", "messageId": "2017", "endLine": 1377, "endColumn": 28}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 1377, "column": 58, "nodeType": "2016", "messageId": "2017", "endLine": 1377, "endColumn": 60}, {"ruleId": "1861", "severity": 1, "message": "2037", "line": 1508, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 1508, "endColumn": 33}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 1585, "column": 19, "nodeType": "2016", "messageId": "2017", "endLine": 1585, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2038", "line": 1732, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 1732, "endColumn": 30}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 1994, "column": 19, "nodeType": "2016", "messageId": "2017", "endLine": 1994, "endColumn": 21}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 2166, "column": 25, "nodeType": "2016", "messageId": "2017", "endLine": 2166, "endColumn": 27}, {"ruleId": "1964", "severity": 1, "message": "2039", "line": 2196, "column": 5, "nodeType": "1966", "endLine": 2196, "endColumn": 18, "suggestions": "2040"}, {"ruleId": "1861", "severity": 1, "message": "2041", "line": 2253, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 2253, "endColumn": 36}, {"ruleId": "1861", "severity": 1, "message": "2042", "line": 2260, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 2260, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2043", "line": 2263, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 2263, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "2044", "line": 2263, "column": 29, "nodeType": "1863", "messageId": "1864", "endLine": 2263, "endColumn": 48}, {"ruleId": "1861", "severity": 1, "message": "2045", "line": 2655, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 2655, "endColumn": 27}, {"ruleId": "1964", "severity": 1, "message": "2046", "line": 2690, "column": 5, "nodeType": "1966", "endLine": 2690, "endColumn": 38, "suggestions": "2047"}, {"ruleId": "1861", "severity": 1, "message": "2048", "line": 2707, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 2707, "endColumn": 28}, {"ruleId": "1861", "severity": 1, "message": "2049", "line": 2741, "column": 6, "nodeType": "1863", "messageId": "1864", "endLine": 2741, "endColumn": 18}, {"ruleId": "1964", "severity": 1, "message": "2050", "line": 3125, "column": 4, "nodeType": "1966", "endLine": 3125, "endColumn": 18, "suggestions": "2051"}, {"ruleId": "1861", "severity": 1, "message": "2052", "line": 3462, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 3462, "endColumn": 33}, {"ruleId": "1964", "severity": 1, "message": "2053", "line": 3536, "column": 16, "nodeType": "2021", "endLine": 3536, "endColumn": 37}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 3539, "column": 49, "nodeType": "2016", "messageId": "2017", "endLine": 3539, "endColumn": 51}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 3543, "column": 50, "nodeType": "2016", "messageId": "2017", "endLine": 3543, "endColumn": 52}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 3549, "column": 51, "nodeType": "2016", "messageId": "2017", "endLine": 3549, "endColumn": 53}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 3556, "column": 51, "nodeType": "2016", "messageId": "2017", "endLine": 3556, "endColumn": 53}, {"ruleId": "1861", "severity": 1, "message": "2054", "line": 3777, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 3777, "endColumn": 23}, {"ruleId": "2014", "severity": 1, "message": "2015", "line": 3785, "column": 30, "nodeType": "2016", "messageId": "2017", "endLine": 3785, "endColumn": 32}, {"ruleId": "1861", "severity": 1, "message": "2055", "line": 3798, "column": 12, "nodeType": "1863", "messageId": "1864", "endLine": 3798, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2056", "line": 3812, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 3812, "endColumn": 28}, {"ruleId": "1861", "severity": 1, "message": "2057", "line": 3812, "column": 30, "nodeType": "1863", "messageId": "1864", "endLine": 3812, "endColumn": 52}, {"ruleId": "1861", "severity": 1, "message": "2058", "line": 3926, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 3926, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2059", "line": 3928, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 3928, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "2060", "line": 3932, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 3932, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2061", "line": 3951, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 3951, "endColumn": 26}, {"ruleId": "2014", "severity": 1, "message": "2015", "line": 3977, "column": 66, "nodeType": "2016", "messageId": "2017", "endLine": 3977, "endColumn": 68}, {"ruleId": "1964", "severity": 1, "message": "2062", "line": 3984, "column": 5, "nodeType": "1966", "endLine": 3991, "endColumn": 3, "suggestions": "2063"}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 4225, "column": 17, "nodeType": "2016", "messageId": "2017", "endLine": 4225, "endColumn": 19}, {"ruleId": "2014", "severity": 1, "message": "2015", "line": 4479, "column": 21, "nodeType": "2016", "messageId": "2017", "endLine": 4479, "endColumn": 23}, {"ruleId": "2014", "severity": 1, "message": "2015", "line": 4487, "column": 21, "nodeType": "2016", "messageId": "2017", "endLine": 4487, "endColumn": 23}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 4500, "column": 15, "nodeType": "2016", "messageId": "2017", "endLine": 4500, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2064", "line": 4797, "column": 11, "nodeType": "1863", "messageId": "1864", "endLine": 4797, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2065", "line": 4808, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 4808, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2066", "line": 4809, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 4809, "endColumn": 20}, {"ruleId": "1964", "severity": 1, "message": "2067", "line": 4815, "column": 5, "nodeType": "1966", "endLine": 4815, "endColumn": 62, "suggestions": "2068"}, {"ruleId": "1964", "severity": 1, "message": "2020", "line": 4815, "column": 6, "nodeType": "2069", "endLine": 4815, "endColumn": 48}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 4838, "column": 25, "nodeType": "2016", "messageId": "2017", "endLine": 4838, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "2070", "line": 4842, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 4842, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2071", "line": 4865, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 4865, "endColumn": 23}, {"ruleId": "2014", "severity": 1, "message": "2015", "line": 4940, "column": 25, "nodeType": "2016", "messageId": "2017", "endLine": 4940, "endColumn": 27}, {"ruleId": "1964", "severity": 1, "message": "2072", "line": 4973, "column": 5, "nodeType": "1966", "endLine": 4973, "endColumn": 22, "suggestions": "2073"}, {"ruleId": "1861", "severity": 1, "message": "2074", "line": 4975, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 4975, "endColumn": 18}, {"ruleId": "2014", "severity": 1, "message": "2015", "line": 4977, "column": 40, "nodeType": "2016", "messageId": "2017", "endLine": 4977, "endColumn": 42}, {"ruleId": "2014", "severity": 1, "message": "2015", "line": 5042, "column": 69, "nodeType": "2016", "messageId": "2017", "endLine": 5042, "endColumn": 71}, {"ruleId": "1861", "severity": 1, "message": "2075", "line": 5092, "column": 12, "nodeType": "1863", "messageId": "1864", "endLine": 5092, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2076", "line": 5093, "column": 12, "nodeType": "1863", "messageId": "1864", "endLine": 5093, "endColumn": 22}, {"ruleId": "1964", "severity": 1, "message": "2077", "line": 5123, "column": 5, "nodeType": "1966", "endLine": 5123, "endColumn": 38, "suggestions": "2078"}, {"ruleId": "2014", "severity": 1, "message": "2015", "line": 5126, "column": 40, "nodeType": "2016", "messageId": "2017", "endLine": 5126, "endColumn": 42}, {"ruleId": "1861", "severity": 1, "message": "2075", "line": 5132, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 5132, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2076", "line": 5133, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 5133, "endColumn": 20}, {"ruleId": "1964", "severity": 1, "message": "2079", "line": 5139, "column": 5, "nodeType": "1966", "endLine": 5139, "endColumn": 106, "suggestions": "2080"}, {"ruleId": "1964", "severity": 1, "message": "2081", "line": 5252, "column": 5, "nodeType": "1966", "endLine": 5252, "endColumn": 17, "suggestions": "2082"}, {"ruleId": "2083", "severity": 1, "message": "2084", "line": 5854, "column": 80, "nodeType": "2085", "messageId": "2086", "endLine": 5854, "endColumn": 81, "suggestions": "2087"}, {"ruleId": "1861", "severity": 1, "message": "2088", "line": 6059, "column": 25, "nodeType": "1863", "messageId": "1864", "endLine": 6059, "endColumn": 42}, {"ruleId": "1861", "severity": 1, "message": "2089", "line": 2, "column": 16, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "2090", "line": 7, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 7, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2091", "line": 7, "column": 23, "nodeType": "1863", "messageId": "1864", "endLine": 7, "endColumn": 34}, {"ruleId": "1861", "severity": 1, "message": "2092", "line": 93, "column": 13, "nodeType": "1863", "messageId": "1864", "endLine": 93, "endColumn": 25}, {"ruleId": "1964", "severity": 1, "message": "2093", "line": 98, "column": 6, "nodeType": "1966", "endLine": 98, "endColumn": 8, "suggestions": "2094"}, {"ruleId": "1861", "severity": 1, "message": "2095", "line": 120, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 120, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2096", "line": 3, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 3, "endColumn": 31}, {"ruleId": "1861", "severity": 1, "message": "2097", "line": 4, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 32}, {"ruleId": "1861", "severity": 1, "message": "2098", "line": 3, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 3, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "2099", "line": 8, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 8, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "2100", "line": 9, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 9, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2101", "line": 13, "column": 24, "nodeType": "1863", "messageId": "1864", "endLine": 13, "endColumn": 46}, {"ruleId": "2102", "severity": 1, "message": "2103", "line": 2394, "column": 5, "nodeType": "2104", "messageId": "2017", "endLine": 2394, "endColumn": 17}, {"ruleId": "2102", "severity": 1, "message": "2105", "line": 2395, "column": 5, "nodeType": "2104", "messageId": "2017", "endLine": 2395, "endColumn": 20}, {"ruleId": "2102", "severity": 1, "message": "2106", "line": 2726, "column": 5, "nodeType": "2104", "messageId": "2017", "endLine": 2726, "endColumn": 24}, {"ruleId": "2102", "severity": 1, "message": "2107", "line": 2903, "column": 5, "nodeType": "2104", "messageId": "2017", "endLine": 2903, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2108", "line": 3607, "column": 16, "nodeType": "1863", "messageId": "1864", "endLine": 3607, "endColumn": 28}, {"ruleId": "1861", "severity": 1, "message": "2108", "line": 3806, "column": 16, "nodeType": "1863", "messageId": "1864", "endLine": 3806, "endColumn": 28}, {"ruleId": "2102", "severity": 1, "message": "2109", "line": 5241, "column": 5, "nodeType": "2104", "messageId": "2017", "endLine": 5241, "endColumn": 30}, {"ruleId": "1861", "severity": 1, "message": "2110", "line": 5304, "column": 14, "nodeType": "1863", "messageId": "1864", "endLine": 5304, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2111", "line": 6388, "column": 13, "nodeType": "1863", "messageId": "1864", "endLine": 6388, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2111", "line": 6414, "column": 13, "nodeType": "1863", "messageId": "1864", "endLine": 6414, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2111", "line": 6420, "column": 13, "nodeType": "1863", "messageId": "1864", "endLine": 6420, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2111", "line": 6435, "column": 13, "nodeType": "1863", "messageId": "1864", "endLine": 6435, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2110", "line": 7212, "column": 13, "nodeType": "1863", "messageId": "1864", "endLine": 7212, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "2110", "line": 7456, "column": 13, "nodeType": "1863", "messageId": "1864", "endLine": 7456, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "2110", "line": 7627, "column": 13, "nodeType": "1863", "messageId": "1864", "endLine": 7627, "endColumn": 16}, {"ruleId": "2014", "severity": 1, "message": "2015", "line": 8292, "column": 66, "nodeType": "2016", "messageId": "2017", "endLine": 8292, "endColumn": 68}, {"ruleId": "1861", "severity": 1, "message": "2112", "line": 70, "column": 23, "nodeType": "1863", "messageId": "1864", "endLine": 70, "endColumn": 29}, {"ruleId": "1861", "severity": 1, "message": "2113", "line": 1, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2114", "line": 2, "column": 44, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 56}, {"ruleId": "1861", "severity": 1, "message": "2115", "line": 18, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 18, "endColumn": 12}, {"ruleId": "1861", "severity": 1, "message": "2116", "line": 19, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 19, "endColumn": 7}, {"ruleId": "1861", "severity": 1, "message": "2117", "line": 20, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 20, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2118", "line": 21, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 21, "endColumn": 7}, {"ruleId": "1861", "severity": 1, "message": "2119", "line": 24, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 24, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2120", "line": 25, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 25, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2121", "line": 26, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 26, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2122", "line": 31, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 31, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2123", "line": 38, "column": 49, "nodeType": "1863", "messageId": "1864", "endLine": 38, "endColumn": 55}, {"ruleId": "1861", "severity": 1, "message": "2124", "line": 38, "column": 63, "nodeType": "1863", "messageId": "1864", "endLine": 38, "endColumn": 70}, {"ruleId": "1861", "severity": 1, "message": "2125", "line": 46, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 46, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2126", "line": 48, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 48, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2127", "line": 92, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 92, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2128", "line": 93, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 93, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2129", "line": 99, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 99, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2130", "line": 100, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 100, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2131", "line": 104, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 104, "endColumn": 10}, {"ruleId": "1861", "severity": 1, "message": "2132", "line": 108, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 108, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2133", "line": 112, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 112, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2134", "line": 113, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 113, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2135", "line": 114, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 114, "endColumn": 28}, {"ruleId": "1861", "severity": 1, "message": "2136", "line": 115, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 115, "endColumn": 33}, {"ruleId": "1861", "severity": 1, "message": "2137", "line": 116, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 116, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2138", "line": 119, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 119, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2139", "line": 120, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 120, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2140", "line": 162, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 162, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2141", "line": 172, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 172, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2142", "line": 180, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 180, "endColumn": 11}, {"ruleId": "1861", "severity": 1, "message": "2143", "line": 181, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 181, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2144", "line": 182, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 182, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2145", "line": 183, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 183, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2146", "line": 184, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 184, "endColumn": 10}, {"ruleId": "1861", "severity": 1, "message": "2147", "line": 205, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 205, "endColumn": 30}, {"ruleId": "1861", "severity": 1, "message": "2148", "line": 209, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 209, "endColumn": 29}, {"ruleId": "1861", "severity": 1, "message": "2149", "line": 455, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 455, "endColumn": 26}, {"ruleId": "1964", "severity": 1, "message": "2150", "line": 547, "column": 5, "nodeType": "1966", "endLine": 547, "endColumn": 60, "suggestions": "2151"}, {"ruleId": "1861", "severity": 1, "message": "2152", "line": 561, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 561, "endColumn": 22}, {"ruleId": "1964", "severity": 1, "message": "2153", "line": 581, "column": 5, "nodeType": "1966", "endLine": 581, "endColumn": 60, "suggestions": "2154"}, {"ruleId": "1964", "severity": 1, "message": "2155", "line": 598, "column": 4, "nodeType": "1966", "endLine": 598, "endColumn": 6, "suggestions": "2156"}, {"ruleId": "1861", "severity": 1, "message": "2157", "line": 2, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2158", "line": 2, "column": 29, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 39}, {"ruleId": "1861", "severity": 1, "message": "1901", "line": 3, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 3, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "1902", "line": 4, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "1899", "line": 5, "column": 26, "nodeType": "1863", "messageId": "1864", "endLine": 5, "endColumn": 44}, {"ruleId": "1861", "severity": 1, "message": "1900", "line": 5, "column": 46, "nodeType": "1863", "messageId": "1864", "endLine": 5, "endColumn": 55}, {"ruleId": "1861", "severity": 1, "message": "2159", "line": 6, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 6, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "1911", "line": 11, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 11, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2160", "line": 19, "column": 19, "nodeType": "1863", "messageId": "1864", "endLine": 19, "endColumn": 35}, {"ruleId": "1861", "severity": 1, "message": "2161", "line": 22, "column": 5, "nodeType": "1863", "messageId": "1864", "endLine": 22, "endColumn": 42}, {"ruleId": "1861", "severity": 1, "message": "2162", "line": 23, "column": 5, "nodeType": "1863", "messageId": "1864", "endLine": 23, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "2163", "line": 24, "column": 5, "nodeType": "1863", "messageId": "1864", "endLine": 24, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "1944", "line": 33, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 33, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "1945", "line": 33, "column": 28, "nodeType": "1863", "messageId": "1864", "endLine": 33, "endColumn": 42}, {"ruleId": "1861", "severity": 1, "message": "1876", "line": 6, "column": 5, "nodeType": "1863", "messageId": "1864", "endLine": 6, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2164", "line": 9, "column": 5, "nodeType": "1863", "messageId": "1864", "endLine": 9, "endColumn": 7}, {"ruleId": "1861", "severity": 1, "message": "2165", "line": 12, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 12, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2166", "line": 25, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 25, "endColumn": 38}, {"ruleId": "1861", "severity": 1, "message": "2167", "line": 51, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 51, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2168", "line": 52, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 52, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "1983", "line": 53, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 53, "endColumn": 11}, {"ruleId": "1861", "severity": 1, "message": "2169", "line": 54, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 54, "endColumn": 8}, {"ruleId": "1861", "severity": 1, "message": "2170", "line": 55, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 55, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2000", "line": 56, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 56, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2171", "line": 57, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 57, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2172", "line": 58, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 58, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2001", "line": 59, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 59, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2173", "line": 60, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 60, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2174", "line": 61, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 61, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2175", "line": 62, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 62, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2176", "line": 63, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 63, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2177", "line": 64, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 64, "endColumn": 29}, {"ruleId": "1861", "severity": 1, "message": "2134", "line": 65, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 65, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2137", "line": 66, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 66, "endColumn": 23}, {"ruleId": "1964", "severity": 1, "message": "2178", "line": 78, "column": 5, "nodeType": "1966", "endLine": 78, "endColumn": 7, "suggestions": "2179"}, {"ruleId": "1964", "severity": 1, "message": "2180", "line": 96, "column": 5, "nodeType": "1966", "endLine": 96, "endColumn": 28, "suggestions": "2181"}, {"ruleId": "1964", "severity": 1, "message": "2182", "line": 107, "column": 5, "nodeType": "1966", "endLine": 107, "endColumn": 48, "suggestions": "2183"}, {"ruleId": "1861", "severity": 1, "message": "2184", "line": 186, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 186, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2185", "line": 226, "column": 7, "nodeType": "1863", "messageId": "1864", "endLine": 226, "endColumn": 29}, {"ruleId": "1861", "severity": 1, "message": "2186", "line": 296, "column": 7, "nodeType": "1863", "messageId": "1864", "endLine": 296, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2055", "line": 681, "column": 11, "nodeType": "1863", "messageId": "1864", "endLine": 681, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2187", "line": 686, "column": 11, "nodeType": "1863", "messageId": "1864", "endLine": 686, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2188", "line": 1, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2189", "line": 3, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 3, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2190", "line": 4, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "1867", "line": 2, "column": 28, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 35}, {"ruleId": "1861", "severity": 1, "message": "2191", "line": 4, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2192", "line": 8, "column": 33, "nodeType": "1863", "messageId": "1864", "endLine": 8, "endColumn": 42}, {"ruleId": "1861", "severity": 1, "message": "2193", "line": 8, "column": 44, "nodeType": "1863", "messageId": "1864", "endLine": 8, "endColumn": 59}, {"ruleId": "1861", "severity": 1, "message": "2194", "line": 10, "column": 34, "nodeType": "1863", "messageId": "1864", "endLine": 10, "endColumn": 57}, {"ruleId": "1861", "severity": 1, "message": "2195", "line": 10, "column": 59, "nodeType": "1863", "messageId": "1864", "endLine": 10, "endColumn": 79}, {"ruleId": "1861", "severity": 1, "message": "2196", "line": 59, "column": 16, "nodeType": "1863", "messageId": "1864", "endLine": 59, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2197", "line": 124, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 124, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "1867", "line": 1, "column": 28, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 35}, {"ruleId": "1861", "severity": 1, "message": "2198", "line": 8, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 8, "endColumn": 33}, {"ruleId": "1861", "severity": 1, "message": "2192", "line": 9, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 9, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2199", "line": 80, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 80, "endColumn": 58}, {"ruleId": "1964", "severity": 1, "message": "2200", "line": 86, "column": 8, "nodeType": "2028", "endLine": 90, "endColumn": 12}, {"ruleId": "1964", "severity": 1, "message": "2201", "line": 86, "column": 8, "nodeType": "2028", "endLine": 90, "endColumn": 12}, {"ruleId": "1861", "severity": 1, "message": "2202", "line": 92, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 92, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2203", "line": 92, "column": 25, "nodeType": "1863", "messageId": "1864", "endLine": 92, "endColumn": 42}, {"ruleId": "2204", "severity": 1, "message": "2205", "line": 113, "column": 113, "nodeType": "2206", "messageId": "2207", "endLine": 113, "endColumn": 397}, {"ruleId": "1964", "severity": 1, "message": "2208", "line": 154, "column": 5, "nodeType": "1966", "endLine": 154, "endColumn": 38, "suggestions": "2209"}, {"ruleId": "1964", "severity": 1, "message": "2020", "line": 154, "column": 6, "nodeType": "2016", "endLine": 154, "endColumn": 37}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 154, "column": 33, "nodeType": "2016", "messageId": "2017", "endLine": 154, "endColumn": 35}, {"ruleId": "1861", "severity": 1, "message": "2196", "line": 156, "column": 16, "nodeType": "1863", "messageId": "1864", "endLine": 156, "endColumn": 24}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 174, "column": 56, "nodeType": "2016", "messageId": "2017", "endLine": 174, "endColumn": 58}, {"ruleId": "1861", "severity": 1, "message": "2210", "line": 181, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 181, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2211", "line": 182, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 182, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2212", "line": 305, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 305, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2196", "line": 770, "column": 16, "nodeType": "1863", "messageId": "1864", "endLine": 770, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2213", "line": 805, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 805, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2214", "line": 805, "column": 19, "nodeType": "1863", "messageId": "1864", "endLine": 805, "endColumn": 30}, {"ruleId": "1861", "severity": 1, "message": "2215", "line": 806, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 806, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2216", "line": 806, "column": 22, "nodeType": "1863", "messageId": "1864", "endLine": 806, "endColumn": 36}, {"ruleId": "1861", "severity": 1, "message": "2043", "line": 807, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 807, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "2044", "line": 807, "column": 29, "nodeType": "1863", "messageId": "1864", "endLine": 807, "endColumn": 48}, {"ruleId": "1861", "severity": 1, "message": "2217", "line": 808, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 808, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2218", "line": 808, "column": 27, "nodeType": "1863", "messageId": "1864", "endLine": 808, "endColumn": 46}, {"ruleId": "1861", "severity": 1, "message": "1958", "line": 809, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 809, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2219", "line": 3, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 3, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "2220", "line": 4, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "2221", "line": 31, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 31, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2222", "line": 32, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 32, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2223", "line": 1, "column": 17, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "1871", "line": 2, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2224", "line": 4, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "1893", "line": 8, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 8, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "2225", "line": 10, "column": 5, "nodeType": "1863", "messageId": "1864", "endLine": 10, "endColumn": 9}, {"ruleId": "1861", "severity": 1, "message": "2157", "line": 11, "column": 5, "nodeType": "1863", "messageId": "1864", "endLine": 11, "endColumn": 8}, {"ruleId": "1861", "severity": 1, "message": "2226", "line": 13, "column": 5, "nodeType": "1863", "messageId": "1864", "endLine": 13, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2121", "line": 15, "column": 5, "nodeType": "1863", "messageId": "1864", "endLine": 15, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "2227", "line": 17, "column": 5, "nodeType": "1863", "messageId": "1864", "endLine": 17, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "2228", "line": 18, "column": 5, "nodeType": "1863", "messageId": "1864", "endLine": 18, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2229", "line": 19, "column": 5, "nodeType": "1863", "messageId": "1864", "endLine": 19, "endColumn": 11}, {"ruleId": "1861", "severity": 1, "message": "2230", "line": 20, "column": 5, "nodeType": "1863", "messageId": "1864", "endLine": 20, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2231", "line": 21, "column": 5, "nodeType": "1863", "messageId": "1864", "endLine": 21, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2232", "line": 22, "column": 5, "nodeType": "1863", "messageId": "1864", "endLine": 22, "endColumn": 12}, {"ruleId": "1861", "severity": 1, "message": "2233", "line": 23, "column": 5, "nodeType": "1863", "messageId": "1864", "endLine": 23, "endColumn": 10}, {"ruleId": "1861", "severity": 1, "message": "2234", "line": 24, "column": 5, "nodeType": "1863", "messageId": "1864", "endLine": 24, "endColumn": 9}, {"ruleId": "1861", "severity": 1, "message": "1896", "line": 3, "column": 65, "nodeType": "1863", "messageId": "1864", "endLine": 3, "endColumn": 69}, {"ruleId": "1861", "severity": 1, "message": "2235", "line": 6, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 6, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2236", "line": 7, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 7, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "2237", "line": 20, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 20, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2214", "line": 84, "column": 19, "nodeType": "1863", "messageId": "1864", "endLine": 84, "endColumn": 30}, {"ruleId": "1861", "severity": 1, "message": "2238", "line": 85, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 85, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2239", "line": 85, "column": 26, "nodeType": "1863", "messageId": "1864", "endLine": 85, "endColumn": 44}, {"ruleId": "1861", "severity": 1, "message": "2240", "line": 86, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 86, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2241", "line": 86, "column": 26, "nodeType": "1863", "messageId": "1864", "endLine": 86, "endColumn": 44}, {"ruleId": "1861", "severity": 1, "message": "2242", "line": 90, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 90, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "2243", "line": 90, "column": 45, "nodeType": "1863", "messageId": "1864", "endLine": 90, "endColumn": 62}, {"ruleId": "1861", "severity": 1, "message": "2244", "line": 93, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 93, "endColumn": 10}, {"ruleId": "1861", "severity": 1, "message": "2245", "line": 94, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 94, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "1951", "line": 95, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 95, "endColumn": 11}, {"ruleId": "1861", "severity": 1, "message": "1952", "line": 96, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 96, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "1953", "line": 97, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 97, "endColumn": 9}, {"ruleId": "1861", "severity": 1, "message": "1954", "line": 98, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 98, "endColumn": 12}, {"ruleId": "1861", "severity": 1, "message": "1955", "line": 99, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 99, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "1956", "line": 100, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 100, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "2246", "line": 101, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 101, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2247", "line": 102, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 102, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2248", "line": 103, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 103, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "1970", "line": 104, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 104, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2249", "line": 105, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 105, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2250", "line": 107, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 107, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2251", "line": 108, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 108, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2252", "line": 115, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 115, "endColumn": 28}, {"ruleId": "1861", "severity": 1, "message": "2253", "line": 116, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 116, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2254", "line": 118, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 118, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2255", "line": 119, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 119, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2256", "line": 120, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 120, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2257", "line": 121, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 121, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2258", "line": 122, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 122, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2259", "line": 123, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 123, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2260", "line": 132, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 132, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2261", "line": 137, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 137, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2128", "line": 139, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 139, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2135", "line": 140, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 140, "endColumn": 28}, {"ruleId": "1861", "severity": 1, "message": "2262", "line": 141, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 141, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2263", "line": 144, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 144, "endColumn": 11}, {"ruleId": "1861", "severity": 1, "message": "2133", "line": 145, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 145, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2264", "line": 146, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 146, "endColumn": 20}, {"ruleId": "1964", "severity": 1, "message": "2265", "line": 170, "column": 5, "nodeType": "1966", "endLine": 170, "endColumn": 45, "suggestions": "2266"}, {"ruleId": "1861", "severity": 1, "message": "2267", "line": 221, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 221, "endColumn": 29}, {"ruleId": "2014", "severity": 1, "message": "2015", "line": 241, "column": 24, "nodeType": "2016", "messageId": "2017", "endLine": 241, "endColumn": 26}, {"ruleId": "1964", "severity": 1, "message": "2268", "line": 315, "column": 7, "nodeType": "1966", "endLine": 315, "endColumn": 42, "suggestions": "2269"}, {"ruleId": "1861", "severity": 1, "message": "2270", "line": 339, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 339, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2271", "line": 340, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 340, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2272", "line": 488, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 488, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2273", "line": 491, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 491, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2274", "line": 500, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 500, "endColumn": 31}, {"ruleId": "2014", "severity": 1, "message": "2015", "line": 814, "column": 26, "nodeType": "2016", "messageId": "2017", "endLine": 814, "endColumn": 28}, {"ruleId": "2014", "severity": 1, "message": "2015", "line": 850, "column": 26, "nodeType": "2016", "messageId": "2017", "endLine": 850, "endColumn": 28}, {"ruleId": "1861", "severity": 1, "message": "2275", "line": 1035, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 1035, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2276", "line": 1039, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 1039, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2277", "line": 1043, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 1043, "endColumn": 28}, {"ruleId": "1861", "severity": 1, "message": "2278", "line": 1047, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 1047, "endColumn": 32}, {"ruleId": "1861", "severity": 1, "message": "2279", "line": 1051, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 1051, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "2280", "line": 1055, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 1055, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2281", "line": 1059, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 1059, "endColumn": 31}, {"ruleId": "1861", "severity": 1, "message": "2282", "line": 1063, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 1063, "endColumn": 28}, {"ruleId": "1861", "severity": 1, "message": "2283", "line": 5, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 5, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2284", "line": 13, "column": 26, "nodeType": "1863", "messageId": "1864", "endLine": 13, "endColumn": 47}, {"ruleId": "1861", "severity": 1, "message": "2285", "line": 15, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 15, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2286", "line": 79, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 79, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2287", "line": 81, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 81, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2288", "line": 82, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 82, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2289", "line": 83, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 83, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2290", "line": 84, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 84, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "1978", "line": 88, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 88, "endColumn": 10}, {"ruleId": "1861", "severity": 1, "message": "2291", "line": 89, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 89, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2292", "line": 91, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 91, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "1980", "line": 92, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 92, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "1981", "line": 93, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 93, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "1979", "line": 94, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 94, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2138", "line": 104, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 104, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2293", "line": 112, "column": 13, "nodeType": "1863", "messageId": "1864", "endLine": 112, "endColumn": 22}, {"ruleId": "1964", "severity": 1, "message": "2294", "line": 209, "column": 7, "nodeType": "1966", "endLine": 209, "endColumn": 9, "suggestions": "2295"}, {"ruleId": "1964", "severity": 1, "message": "2296", "line": 244, "column": 7, "nodeType": "1966", "endLine": 244, "endColumn": 29, "suggestions": "2297"}, {"ruleId": "1964", "severity": 1, "message": "2298", "line": 249, "column": 7, "nodeType": "1966", "endLine": 249, "endColumn": 18, "suggestions": "2299"}, {"ruleId": "1964", "severity": 1, "message": "2300", "line": 292, "column": 7, "nodeType": "1966", "endLine": 292, "endColumn": 72, "suggestions": "2301"}, {"ruleId": "1861", "severity": 1, "message": "2249", "line": 331, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 331, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2302", "line": 334, "column": 26, "nodeType": "1863", "messageId": "1864", "endLine": 334, "endColumn": 44}, {"ruleId": "1861", "severity": 1, "message": "2303", "line": 463, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 463, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2304", "line": 4, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "2305", "line": 6, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 6, "endColumn": 35}, {"ruleId": "1861", "severity": 1, "message": "2306", "line": 7, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 7, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2307", "line": 7, "column": 17, "nodeType": "1863", "messageId": "1864", "endLine": 7, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "2308", "line": 8, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 8, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2309", "line": 9, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 9, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "1980", "line": 13, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 13, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "1981", "line": 14, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 14, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "1979", "line": 15, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 15, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2310", "line": 17, "column": 21, "nodeType": "1863", "messageId": "1864", "endLine": 17, "endColumn": 34}, {"ruleId": "1861", "severity": 1, "message": "2311", "line": 18, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 18, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2312", "line": 18, "column": 33, "nodeType": "1863", "messageId": "1864", "endLine": 18, "endColumn": 44}, {"ruleId": "1861", "severity": 1, "message": "2313", "line": 19, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 19, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2314", "line": 96, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 96, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2315", "line": 97, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 97, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2316", "line": 100, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 100, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2317", "line": 101, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 101, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2318", "line": 109, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 109, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2319", "line": 129, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 129, "endColumn": 16}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 176, "column": 45, "nodeType": "2016", "messageId": "2017", "endLine": 176, "endColumn": 47}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 176, "column": 104, "nodeType": "2016", "messageId": "2017", "endLine": 176, "endColumn": 106}, {"ruleId": "1861", "severity": 1, "message": "2122", "line": 2, "column": 60, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 73}, {"ruleId": "1861", "severity": 1, "message": "2304", "line": 3, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 3, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "2320", "line": 8, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 8, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2321", "line": 9, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 9, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2322", "line": 133, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 133, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2323", "line": 134, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 134, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2324", "line": 135, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 135, "endColumn": 28}, {"ruleId": "1861", "severity": 1, "message": "2325", "line": 135, "column": 30, "nodeType": "1863", "messageId": "1864", "endLine": 135, "endColumn": 52}, {"ruleId": "1861", "severity": 1, "message": "2138", "line": 137, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 137, "endColumn": 19}, {"ruleId": "1964", "severity": 1, "message": "2326", "line": 163, "column": 8, "nodeType": "1966", "endLine": 163, "endColumn": 10, "suggestions": "2327"}, {"ruleId": "1861", "severity": 1, "message": "2328", "line": 299, "column": 11, "nodeType": "1863", "messageId": "1864", "endLine": 299, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2329", "line": 342, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 342, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2330", "line": 343, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 343, "endColumn": 35}, {"ruleId": "1861", "severity": 1, "message": "2331", "line": 344, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 344, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2332", "line": 346, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 346, "endColumn": 20}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 465, "column": 22, "nodeType": "2016", "messageId": "2017", "endLine": 465, "endColumn": 24}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 465, "column": 53, "nodeType": "2016", "messageId": "2017", "endLine": 465, "endColumn": 55}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 465, "column": 89, "nodeType": "2016", "messageId": "2017", "endLine": 465, "endColumn": 91}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 465, "column": 125, "nodeType": "2016", "messageId": "2017", "endLine": 465, "endColumn": 127}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 467, "column": 29, "nodeType": "2016", "messageId": "2017", "endLine": 467, "endColumn": 31}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 467, "column": 56, "nodeType": "2016", "messageId": "2017", "endLine": 467, "endColumn": 58}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 467, "column": 88, "nodeType": "2016", "messageId": "2017", "endLine": 467, "endColumn": 90}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 467, "column": 120, "nodeType": "2016", "messageId": "2017", "endLine": 467, "endColumn": 122}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 469, "column": 29, "nodeType": "2016", "messageId": "2017", "endLine": 469, "endColumn": 31}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 469, "column": 64, "nodeType": "2016", "messageId": "2017", "endLine": 469, "endColumn": 66}, {"ruleId": "1861", "severity": 1, "message": "2333", "line": 111, "column": 11, "nodeType": "1863", "messageId": "1864", "endLine": 111, "endColumn": 28}, {"ruleId": "1861", "severity": 1, "message": "2259", "line": 152, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 152, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "1992", "line": 153, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 153, "endColumn": 11}, {"ruleId": "1861", "severity": 1, "message": "2334", "line": 159, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 159, "endColumn": 23}, {"ruleId": "2335", "severity": 1, "message": "2336", "line": 225, "column": 25, "nodeType": "1863", "messageId": "2337", "endLine": 225, "endColumn": 34, "suggestions": "2338"}, {"ruleId": "1964", "severity": 1, "message": "2339", "line": 231, "column": 5, "nodeType": "1966", "endLine": 231, "endColumn": 12, "suggestions": "2340"}, {"ruleId": "1964", "severity": 1, "message": "2341", "line": 237, "column": 5, "nodeType": "1966", "endLine": 237, "endColumn": 21, "suggestions": "2342"}, {"ruleId": "1964", "severity": 1, "message": "2343", "line": 472, "column": 5, "nodeType": "1966", "endLine": 472, "endColumn": 70, "suggestions": "2344"}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 547, "column": 19, "nodeType": "2016", "messageId": "2017", "endLine": 547, "endColumn": 21}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 548, "column": 19, "nodeType": "2016", "messageId": "2017", "endLine": 548, "endColumn": 21}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 549, "column": 24, "nodeType": "2016", "messageId": "2017", "endLine": 549, "endColumn": 26}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 550, "column": 24, "nodeType": "2016", "messageId": "2017", "endLine": 550, "endColumn": 26}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 554, "column": 19, "nodeType": "2016", "messageId": "2017", "endLine": 554, "endColumn": 21}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 555, "column": 19, "nodeType": "2016", "messageId": "2017", "endLine": 555, "endColumn": 21}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 556, "column": 24, "nodeType": "2016", "messageId": "2017", "endLine": 556, "endColumn": 26}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 557, "column": 24, "nodeType": "2016", "messageId": "2017", "endLine": 557, "endColumn": 26}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 561, "column": 19, "nodeType": "2016", "messageId": "2017", "endLine": 561, "endColumn": 21}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 562, "column": 24, "nodeType": "2016", "messageId": "2017", "endLine": 562, "endColumn": 26}, {"ruleId": "1964", "severity": 1, "message": "2345", "line": 582, "column": 5, "nodeType": "1966", "endLine": 582, "endColumn": 64, "suggestions": "2346"}, {"ruleId": "1964", "severity": 1, "message": "2020", "line": 582, "column": 6, "nodeType": "2069", "endLine": 582, "endColumn": 34}, {"ruleId": "1861", "severity": 1, "message": "2347", "line": 591, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 591, "endColumn": 21}, {"ruleId": "1964", "severity": 1, "message": "2348", "line": 605, "column": 5, "nodeType": "1966", "endLine": 605, "endColumn": 47, "suggestions": "2349"}, {"ruleId": "1861", "severity": 1, "message": "2347", "line": 614, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 614, "endColumn": 21}, {"ruleId": "1964", "severity": 1, "message": "2348", "line": 627, "column": 5, "nodeType": "1966", "endLine": 627, "endColumn": 47, "suggestions": "2350"}, {"ruleId": "1964", "severity": 1, "message": "2351", "line": 1021, "column": 17, "nodeType": "1863", "endLine": 1021, "endColumn": 32}, {"ruleId": "2014", "severity": 1, "message": "2015", "line": 1227, "column": 43, "nodeType": "2016", "messageId": "2017", "endLine": 1227, "endColumn": 45}, {"ruleId": "2014", "severity": 1, "message": "2015", "line": 1232, "column": 78, "nodeType": "2016", "messageId": "2017", "endLine": 1232, "endColumn": 80}, {"ruleId": "1861", "severity": 1, "message": "2352", "line": 1, "column": 17, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "2353", "line": 2, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2354", "line": 2, "column": 16, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "2098", "line": 3, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 3, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "2355", "line": 11, "column": 62, "nodeType": "1863", "messageId": "1864", "endLine": 11, "endColumn": 67}, {"ruleId": "1861", "severity": 1, "message": "2176", "line": 25, "column": 7, "nodeType": "1863", "messageId": "1864", "endLine": 25, "endColumn": 29}, {"ruleId": "1861", "severity": 1, "message": "2137", "line": 28, "column": 7, "nodeType": "1863", "messageId": "1864", "endLine": 28, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "2356", "line": 31, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 31, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "2357", "line": 144, "column": 11, "nodeType": "1863", "messageId": "1864", "endLine": 144, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2358", "line": 145, "column": 11, "nodeType": "1863", "messageId": "1864", "endLine": 145, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2359", "line": 1, "column": 28, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 34}, {"ruleId": "1861", "severity": 1, "message": "2157", "line": 5, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 5, "endColumn": 5}, {"ruleId": "1861", "severity": 1, "message": "2360", "line": 6, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 6, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2361", "line": 10, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 10, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "2362", "line": 12, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 12, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2158", "line": 13, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 13, "endColumn": 12}, {"ruleId": "1861", "severity": 1, "message": "2363", "line": 17, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 17, "endColumn": 32}, {"ruleId": "1861", "severity": 1, "message": "2198", "line": 19, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 19, "endColumn": 33}, {"ruleId": "1861", "severity": 1, "message": "2364", "line": 33, "column": 11, "nodeType": "1863", "messageId": "1864", "endLine": 33, "endColumn": 31}, {"ruleId": "1861", "severity": 1, "message": "2215", "line": 33, "column": 33, "nodeType": "1863", "messageId": "1864", "endLine": 33, "endColumn": 44}, {"ruleId": "2365", "severity": 1, "message": "2366", "line": 95, "column": 2, "nodeType": "2367", "messageId": "2368", "endLine": 111, "endColumn": 4}, {"ruleId": "1861", "severity": 1, "message": "2369", "line": 132, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 132, "endColumn": 10}, {"ruleId": "1861", "severity": 1, "message": "2370", "line": 135, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 135, "endColumn": 12}, {"ruleId": "1861", "severity": 1, "message": "2247", "line": 136, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 136, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "1970", "line": 137, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 137, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2371", "line": 139, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 139, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2244", "line": 140, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 140, "endColumn": 10}, {"ruleId": "1861", "severity": 1, "message": "2372", "line": 141, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 141, "endColumn": 8}, {"ruleId": "1861", "severity": 1, "message": "2373", "line": 144, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 144, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2374", "line": 145, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 145, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2375", "line": 146, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 146, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2376", "line": 147, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 147, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2377", "line": 148, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 148, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2378", "line": 149, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 149, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2254", "line": 150, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 150, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2253", "line": 151, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 151, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2252", "line": 152, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 152, "endColumn": 28}, {"ruleId": "1861", "severity": 1, "message": "2379", "line": 155, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 155, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2380", "line": 158, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 158, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2381", "line": 159, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 159, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2382", "line": 160, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 160, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2169", "line": 161, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 161, "endColumn": 8}, {"ruleId": "1861", "severity": 1, "message": "2132", "line": 162, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 162, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2383", "line": 165, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 165, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2384", "line": 166, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 166, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2334", "line": 168, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 168, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2385", "line": 173, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 173, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "1992", "line": 174, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 174, "endColumn": 11}, {"ruleId": "1861", "severity": 1, "message": "2386", "line": 185, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 185, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2387", "line": 185, "column": 25, "nodeType": "1863", "messageId": "1864", "endLine": 185, "endColumn": 42}, {"ruleId": "1861", "severity": 1, "message": "2388", "line": 187, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 187, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2389", "line": 187, "column": 26, "nodeType": "1863", "messageId": "1864", "endLine": 187, "endColumn": 44}, {"ruleId": "2390", "severity": 1, "message": "2391", "line": 349, "column": 5, "nodeType": "2392", "messageId": "2393", "endLine": 349, "endColumn": 52}, {"ruleId": "1861", "severity": 1, "message": "2394", "line": 504, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 504, "endColumn": 31}, {"ruleId": "1861", "severity": 1, "message": "2395", "line": 511, "column": 7, "nodeType": "1863", "messageId": "1864", "endLine": 511, "endColumn": 21}, {"ruleId": "1964", "severity": 1, "message": "2396", "line": 668, "column": 5, "nodeType": "1966", "endLine": 668, "endColumn": 100, "suggestions": "2397"}, {"ruleId": "1964", "severity": 1, "message": "2398", "line": 775, "column": 5, "nodeType": "1966", "endLine": 775, "endColumn": 22, "suggestions": "2399"}, {"ruleId": "1861", "severity": 1, "message": "2400", "line": 876, "column": 7, "nodeType": "1863", "messageId": "1864", "endLine": 876, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2352", "line": 1, "column": 17, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "2401", "line": 1, "column": 28, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 36}, {"ruleId": "1861", "severity": 1, "message": "2402", "line": 4, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2167", "line": 14, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 14, "endColumn": 28}, {"ruleId": "1861", "severity": 1, "message": "2173", "line": 15, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 15, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2168", "line": 16, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 16, "endColumn": 32}, {"ruleId": "1861", "severity": 1, "message": "2137", "line": 17, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 17, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2177", "line": 20, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 20, "endColumn": 29}, {"ruleId": "1861", "severity": 1, "message": "2403", "line": 26, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 26, "endColumn": 29}, {"ruleId": "1861", "severity": 1, "message": "2401", "line": 1, "column": 17, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2404", "line": 2, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2098", "line": 2, "column": 19, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2158", "line": 2, "column": 27, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 37}, {"ruleId": "1861", "severity": 1, "message": "2157", "line": 2, "column": 39, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 42}, {"ruleId": "1861", "severity": 1, "message": "2361", "line": 2, "column": 44, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 58}, {"ruleId": "1861", "severity": 1, "message": "2122", "line": 2, "column": 60, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 73}, {"ruleId": "1861", "severity": 1, "message": "2231", "line": 2, "column": 74, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 84}, {"ruleId": "1861", "severity": 1, "message": "2405", "line": 3, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 3, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2406", "line": 4, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2167", "line": 98, "column": 13, "nodeType": "1863", "messageId": "1864", "endLine": 98, "endColumn": 32}, {"ruleId": "1861", "severity": 1, "message": "2173", "line": 99, "column": 13, "nodeType": "1863", "messageId": "1864", "endLine": 99, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "2168", "line": 100, "column": 11, "nodeType": "1863", "messageId": "1864", "endLine": 100, "endColumn": 34}, {"ruleId": "1861", "severity": 1, "message": "1983", "line": 101, "column": 11, "nodeType": "1863", "messageId": "1864", "endLine": 101, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2169", "line": 102, "column": 11, "nodeType": "1863", "messageId": "1864", "endLine": 102, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "2170", "line": 103, "column": 11, "nodeType": "1863", "messageId": "1864", "endLine": 103, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "2000", "line": 104, "column": 11, "nodeType": "1863", "messageId": "1864", "endLine": 104, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2001", "line": 105, "column": 13, "nodeType": "1863", "messageId": "1864", "endLine": 105, "endColumn": 32}, {"ruleId": "1861", "severity": 1, "message": "2216", "line": 110, "column": 13, "nodeType": "1863", "messageId": "1864", "endLine": 110, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "2138", "line": 113, "column": 13, "nodeType": "1863", "messageId": "1864", "endLine": 113, "endColumn": 29}, {"ruleId": "1964", "severity": 1, "message": "2407", "line": 172, "column": 12, "nodeType": "1966", "endLine": 172, "endColumn": 35, "suggestions": "2408"}, {"ruleId": "1861", "severity": 1, "message": "1867", "line": 1, "column": 27, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 34}, {"ruleId": "1861", "severity": 1, "message": "2091", "line": 4, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2190", "line": 7, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 7, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "1909", "line": 7, "column": 26, "nodeType": "1863", "messageId": "1864", "endLine": 7, "endColumn": 37}, {"ruleId": "1861", "severity": 1, "message": "2409", "line": 43, "column": 21, "nodeType": "1863", "messageId": "1864", "endLine": 43, "endColumn": 34}, {"ruleId": "1964", "severity": 1, "message": "2410", "line": 63, "column": 21, "nodeType": "2411", "endLine": 63, "endColumn": 111}, {"ruleId": "1861", "severity": 1, "message": "2412", "line": 2, "column": 25, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 39}, {"ruleId": "1861", "severity": 1, "message": "2413", "line": 4, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2090", "line": 5, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 5, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2414", "line": 11, "column": 7, "nodeType": "1863", "messageId": "1864", "endLine": 11, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2415", "line": 1, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2416", "line": 2, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "1898", "line": 1, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2416", "line": 2, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2417", "line": 2, "column": 27, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 40}, {"ruleId": "1861", "severity": 1, "message": "2158", "line": 2, "column": 23, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 33}, {"ruleId": "1861", "severity": 1, "message": "2418", "line": 3, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 3, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2418", "line": 3, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 3, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2157", "line": 2, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2158", "line": 2, "column": 15, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2231", "line": 2, "column": 50, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 60}, {"ruleId": "1861", "severity": 1, "message": "2405", "line": 29, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 29, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2385", "line": 62, "column": 29, "nodeType": "1863", "messageId": "1864", "endLine": 62, "endColumn": 43}, {"ruleId": "1861", "severity": 1, "message": "2419", "line": 70, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 70, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2420", "line": 72, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 72, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2421", "line": 94, "column": 15, "nodeType": "1863", "messageId": "1864", "endLine": 94, "endColumn": 22}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 103, "column": 34, "nodeType": "2016", "messageId": "2017", "endLine": 103, "endColumn": 36}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 151, "column": 44, "nodeType": "2016", "messageId": "2017", "endLine": 151, "endColumn": 46}, {"ruleId": "1861", "severity": 1, "message": "2422", "line": 170, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 170, "endColumn": 21}, {"ruleId": "1964", "severity": 1, "message": "2423", "line": 293, "column": 5, "nodeType": "1966", "endLine": 293, "endColumn": 50, "suggestions": "2424"}, {"ruleId": "1964", "severity": 1, "message": "2423", "line": 309, "column": 5, "nodeType": "1966", "endLine": 309, "endColumn": 18, "suggestions": "2425"}, {"ruleId": "1861", "severity": 1, "message": "2352", "line": 1, "column": 17, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "2426", "line": 61, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 61, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2427", "line": 63, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 63, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "1867", "line": 1, "column": 57, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 64}, {"ruleId": "1861", "severity": 1, "message": "2428", "line": 2, "column": 15, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2429", "line": 6, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 6, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2430", "line": 358, "column": 27, "nodeType": "1863", "messageId": "1864", "endLine": 358, "endColumn": 45}, {"ruleId": "1861", "severity": 1, "message": "2428", "line": 2, "column": 44, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 53}, {"ruleId": "1861", "severity": 1, "message": "2431", "line": 4, "column": 46, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 65}, {"ruleId": "1861", "severity": 1, "message": "1891", "line": 4, "column": 67, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 75}, {"ruleId": "1861", "severity": 1, "message": "2405", "line": 7, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 7, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2432", "line": 8, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 8, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2167", "line": 30, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 30, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2433", "line": 31, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 31, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "1979", "line": 34, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 34, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2434", "line": 44, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 44, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "2435", "line": 45, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 45, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "2436", "line": 46, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 46, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "2437", "line": 47, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 47, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "2438", "line": 51, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 51, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2439", "line": 51, "column": 21, "nodeType": "1863", "messageId": "1864", "endLine": 51, "endColumn": 34}, {"ruleId": "1861", "severity": 1, "message": "2440", "line": 52, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 52, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2441", "line": 53, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 53, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2442", "line": 56, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 56, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2443", "line": 57, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 57, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2444", "line": 57, "column": 20, "nodeType": "1863", "messageId": "1864", "endLine": 57, "endColumn": 32}, {"ruleId": "1964", "severity": 1, "message": "2445", "line": 65, "column": 5, "nodeType": "1966", "endLine": 65, "endColumn": 7, "suggestions": "2446"}, {"ruleId": "1861", "severity": 1, "message": "2447", "line": 93, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 93, "endColumn": 32}, {"ruleId": "1861", "severity": 1, "message": "2448", "line": 97, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 97, "endColumn": 32}, {"ruleId": "1861", "severity": 1, "message": "2449", "line": 124, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 124, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2450", "line": 132, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 132, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "2451", "line": 136, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 136, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "2452", "line": 150, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 150, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "2453", "line": 153, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 153, "endColumn": 28}, {"ruleId": "1861", "severity": 1, "message": "1867", "line": 5, "column": 28, "nodeType": "1863", "messageId": "1864", "endLine": 5, "endColumn": 35}, {"ruleId": "1861", "severity": 1, "message": "2454", "line": 8, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 8, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2202", "line": 85, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 85, "endColumn": 24}, {"ruleId": "1964", "severity": 1, "message": "2455", "line": 98, "column": 6, "nodeType": "1966", "endLine": 98, "endColumn": 8, "suggestions": "2456"}, {"ruleId": "1964", "severity": 1, "message": "2457", "line": 121, "column": 6, "nodeType": "1966", "endLine": 121, "endColumn": 32, "suggestions": "2458"}, {"ruleId": "1964", "severity": 1, "message": "2208", "line": 125, "column": 6, "nodeType": "1966", "endLine": 125, "endColumn": 40, "suggestions": "2459"}, {"ruleId": "1964", "severity": 1, "message": "2020", "line": 125, "column": 7, "nodeType": "2016", "endLine": 125, "endColumn": 39}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 125, "column": 35, "nodeType": "2016", "messageId": "2017", "endLine": 125, "endColumn": 37}, {"ruleId": "1964", "severity": 1, "message": "2460", "line": 148, "column": 6, "nodeType": "1966", "endLine": 148, "endColumn": 33, "suggestions": "2461"}, {"ruleId": "1964", "severity": 1, "message": "2020", "line": 148, "column": 7, "nodeType": "2021", "endLine": 148, "endColumn": 32}, {"ruleId": "1861", "severity": 1, "message": "2462", "line": 156, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 156, "endColumn": 29}, {"ruleId": "1861", "severity": 1, "message": "2463", "line": 2, "column": 14, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2464", "line": 14, "column": 5, "nodeType": "1863", "messageId": "1864", "endLine": 14, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2465", "line": 18, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 18, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2466", "line": 4, "column": 23, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 30}, {"ruleId": "1861", "severity": 1, "message": "2467", "line": 4, "column": 32, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 45}, {"ruleId": "1861", "severity": 1, "message": "2468", "line": 10, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 10, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2469", "line": 65, "column": 12, "nodeType": "1863", "messageId": "1864", "endLine": 65, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2470", "line": 65, "column": 24, "nodeType": "1863", "messageId": "1864", "endLine": 65, "endColumn": 37}, {"ruleId": "1861", "severity": 1, "message": "2471", "line": 78, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 78, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2472", "line": 78, "column": 24, "nodeType": "1863", "messageId": "1864", "endLine": 78, "endColumn": 39}, {"ruleId": "1964", "severity": 1, "message": "2473", "line": 120, "column": 6, "nodeType": "1966", "endLine": 120, "endColumn": 8, "suggestions": "2474"}, {"ruleId": "1861", "severity": 1, "message": "2475", "line": 157, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 157, "endColumn": 32}, {"ruleId": "1861", "severity": 1, "message": "2476", "line": 280, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 280, "endColumn": 29}, {"ruleId": "1861", "severity": 1, "message": "2477", "line": 296, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 296, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2478", "line": 461, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 461, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2479", "line": 462, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 462, "endColumn": 20}, {"ruleId": "1964", "severity": 1, "message": "2480", "line": 467, "column": 3, "nodeType": "1966", "endLine": 467, "endColumn": 5, "suggestions": "2481"}, {"ruleId": "1861", "severity": 1, "message": "2482", "line": 1, "column": 17, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "2225", "line": 2, "column": 38, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 42}, {"ruleId": "1861", "severity": 1, "message": "1893", "line": 2, "column": 64, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 78}, {"ruleId": "1861", "severity": 1, "message": "2227", "line": 2, "column": 80, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 91}, {"ruleId": "1861", "severity": 1, "message": "2228", "line": 2, "column": 93, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 103}, {"ruleId": "1861", "severity": 1, "message": "2229", "line": 2, "column": 105, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 111}, {"ruleId": "1861", "severity": 1, "message": "2230", "line": 2, "column": 113, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 121}, {"ruleId": "1861", "severity": 1, "message": "2483", "line": 2, "column": 123, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 140}, {"ruleId": "1861", "severity": 1, "message": "2117", "line": 2, "column": 142, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 158}, {"ruleId": "1861", "severity": 1, "message": "2484", "line": 2, "column": 160, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 166}, {"ruleId": "1861", "severity": 1, "message": "2485", "line": 2, "column": 168, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 180}, {"ruleId": "1861", "severity": 1, "message": "2486", "line": 2, "column": 182, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 199}, {"ruleId": "1861", "severity": 1, "message": "2487", "line": 4, "column": 26, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 49}, {"ruleId": "1861", "severity": 1, "message": "2488", "line": 4, "column": 51, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 71}, {"ruleId": "1861", "severity": 1, "message": "2489", "line": 4, "column": 73, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 91}, {"ruleId": "1861", "severity": 1, "message": "2490", "line": 5, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 5, "endColumn": 31}, {"ruleId": "1861", "severity": 1, "message": "2491", "line": 13, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 13, "endColumn": 12}, {"ruleId": "1861", "severity": 1, "message": "2492", "line": 14, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 14, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2493", "line": 15, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 15, "endColumn": 10}, {"ruleId": "1861", "severity": 1, "message": "1891", "line": 16, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 16, "endColumn": 10}, {"ruleId": "1861", "severity": 1, "message": "2494", "line": 17, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 17, "endColumn": 7}, {"ruleId": "1861", "severity": 1, "message": "2495", "line": 24, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 24, "endColumn": 28}, {"ruleId": "1861", "severity": 1, "message": "2496", "line": 25, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 25, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2497", "line": 26, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 26, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2498", "line": 27, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 27, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2499", "line": 28, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 28, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2500", "line": 29, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 29, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2501", "line": 30, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 30, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2502", "line": 40, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 40, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2503", "line": 41, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 41, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "2504", "line": 43, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 43, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2505", "line": 45, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 45, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "2210", "line": 47, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 47, "endColumn": 12}, {"ruleId": "1861", "severity": 1, "message": "2506", "line": 94, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 94, "endColumn": 19}, {"ruleId": "1964", "severity": 1, "message": "2507", "line": 125, "column": 5, "nodeType": "1966", "endLine": 125, "endColumn": 7, "suggestions": "2508"}, {"ruleId": "1861", "severity": 1, "message": "2509", "line": 145, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 145, "endColumn": 30}, {"ruleId": "1861", "severity": 1, "message": "2510", "line": 162, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 162, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2511", "line": 165, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 165, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2512", "line": 170, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 170, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2513", "line": 211, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 211, "endColumn": 29}, {"ruleId": "1861", "severity": 1, "message": "2514", "line": 214, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 214, "endColumn": 33}, {"ruleId": "1861", "severity": 1, "message": "2515", "line": 227, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 227, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2516", "line": 228, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 228, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2517", "line": 228, "column": 15, "nodeType": "1863", "messageId": "1864", "endLine": 228, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2518", "line": 229, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 229, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2519", "line": 229, "column": 20, "nodeType": "1863", "messageId": "1864", "endLine": 229, "endColumn": 32}, {"ruleId": "1861", "severity": 1, "message": "1946", "line": 245, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 245, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "1947", "line": 245, "column": 16, "nodeType": "1863", "messageId": "1864", "endLine": 245, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2520", "line": 247, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 247, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2521", "line": 261, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 261, "endColumn": 36}, {"ruleId": "1964", "severity": 1, "message": "2522", "line": 281, "column": 4, "nodeType": "1966", "endLine": 281, "endColumn": 6, "suggestions": "2523"}, {"ruleId": "1861", "severity": 1, "message": "2521", "line": 334, "column": 12, "nodeType": "1863", "messageId": "1864", "endLine": 334, "endColumn": 38}, {"ruleId": "1861", "severity": 1, "message": "2524", "line": 347, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 347, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2525", "line": 347, "column": 27, "nodeType": "1863", "messageId": "1864", "endLine": 347, "endColumn": 45}, {"ruleId": "1861", "severity": 1, "message": "2482", "line": 1, "column": 17, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "2225", "line": 2, "column": 38, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 42}, {"ruleId": "1861", "severity": 1, "message": "1893", "line": 2, "column": 64, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 78}, {"ruleId": "1861", "severity": 1, "message": "2227", "line": 2, "column": 80, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 91}, {"ruleId": "1861", "severity": 1, "message": "2228", "line": 2, "column": 93, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 103}, {"ruleId": "1861", "severity": 1, "message": "2229", "line": 2, "column": 105, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 111}, {"ruleId": "1861", "severity": 1, "message": "2230", "line": 2, "column": 113, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 121}, {"ruleId": "1861", "severity": 1, "message": "2483", "line": 2, "column": 123, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 140}, {"ruleId": "1861", "severity": 1, "message": "2117", "line": 2, "column": 142, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 158}, {"ruleId": "1861", "severity": 1, "message": "2484", "line": 2, "column": 160, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 166}, {"ruleId": "1861", "severity": 1, "message": "2232", "line": 2, "column": 168, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 175}, {"ruleId": "1861", "severity": 1, "message": "2487", "line": 4, "column": 26, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 49}, {"ruleId": "1861", "severity": 1, "message": "2488", "line": 4, "column": 51, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 71}, {"ruleId": "1861", "severity": 1, "message": "2489", "line": 4, "column": 73, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 91}, {"ruleId": "1861", "severity": 1, "message": "2490", "line": 5, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 5, "endColumn": 31}, {"ruleId": "1861", "severity": 1, "message": "2491", "line": 8, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 8, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2492", "line": 9, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 9, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2493", "line": 10, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 10, "endColumn": 11}, {"ruleId": "1861", "severity": 1, "message": "2494", "line": 11, "column": 5, "nodeType": "1863", "messageId": "1864", "endLine": 11, "endColumn": 10}, {"ruleId": "1861", "severity": 1, "message": "1891", "line": 12, "column": 5, "nodeType": "1863", "messageId": "1864", "endLine": 12, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2526", "line": 13, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 13, "endColumn": 12}, {"ruleId": "1861", "severity": 1, "message": "2527", "line": 14, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 14, "endColumn": 12}, {"ruleId": "1861", "severity": 1, "message": "2528", "line": 15, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 15, "endColumn": 12}, {"ruleId": "1861", "severity": 1, "message": "2529", "line": 22, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 22, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2530", "line": 31, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 31, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2194", "line": 32, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 32, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "2502", "line": 35, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 35, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2503", "line": 36, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 36, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "2504", "line": 38, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 38, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2531", "line": 39, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 39, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "2532", "line": 40, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 40, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2533", "line": 42, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 42, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2534", "line": 43, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 43, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "2535", "line": 44, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 44, "endColumn": 29}, {"ruleId": "1861", "severity": 1, "message": "2536", "line": 45, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 45, "endColumn": 32}, {"ruleId": "1861", "severity": 1, "message": "2537", "line": 46, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 46, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2538", "line": 47, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 47, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2539", "line": 48, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 48, "endColumn": 29}, {"ruleId": "1861", "severity": 1, "message": "2540", "line": 49, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 49, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "2541", "line": 50, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 50, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2542", "line": 51, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 51, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2419", "line": 58, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 58, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2543", "line": 60, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 60, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2506", "line": 75, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 75, "endColumn": 19}, {"ruleId": "1964", "severity": 1, "message": "2460", "line": 87, "column": 5, "nodeType": "1966", "endLine": 87, "endColumn": 45, "suggestions": "2544"}, {"ruleId": "1964", "severity": 1, "message": "2020", "line": 87, "column": 6, "nodeType": "2069", "endLine": 87, "endColumn": 44}, {"ruleId": "1861", "severity": 1, "message": "2545", "line": 106, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 106, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2546", "line": 106, "column": 23, "nodeType": "1863", "messageId": "1864", "endLine": 106, "endColumn": 38}, {"ruleId": "1861", "severity": 1, "message": "2547", "line": 107, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 107, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2548", "line": 107, "column": 15, "nodeType": "1863", "messageId": "1864", "endLine": 107, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2549", "line": 108, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 108, "endColumn": 11}, {"ruleId": "1861", "severity": 1, "message": "2131", "line": 109, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 109, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "2550", "line": 109, "column": 18, "nodeType": "1863", "messageId": "1864", "endLine": 109, "endColumn": 28}, {"ruleId": "1861", "severity": 1, "message": "2551", "line": 110, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 110, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2510", "line": 115, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 115, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2512", "line": 119, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 119, "endColumn": 25}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 134, "column": 11, "nodeType": "2016", "messageId": "2017", "endLine": 134, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2232", "line": 2, "column": 64, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 71}, {"ruleId": "1861", "severity": 1, "message": "2552", "line": 4, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 32}, {"ruleId": "1861", "severity": 1, "message": "2553", "line": 5, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 5, "endColumn": 30}, {"ruleId": "1861", "severity": 1, "message": "2554", "line": 10, "column": 30, "nodeType": "1863", "messageId": "1864", "endLine": 10, "endColumn": 39}, {"ruleId": "1861", "severity": 1, "message": "2158", "line": 2, "column": 15, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2485", "line": 2, "column": 27, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 39}, {"ruleId": "1861", "severity": 1, "message": "2486", "line": 2, "column": 41, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 58}, {"ruleId": "1861", "severity": 1, "message": "2117", "line": 2, "column": 72, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 88}, {"ruleId": "1861", "severity": 1, "message": "2484", "line": 2, "column": 90, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 96}, {"ruleId": "1861", "severity": 1, "message": "2555", "line": 9, "column": 11, "nodeType": "1863", "messageId": "1864", "endLine": 9, "endColumn": 31}, {"ruleId": "1861", "severity": 1, "message": "2484", "line": 6, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 6, "endColumn": 8}, {"ruleId": "1861", "severity": 1, "message": "2229", "line": 9, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 9, "endColumn": 8}, {"ruleId": "1861", "severity": 1, "message": "2230", "line": 10, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 10, "endColumn": 10}, {"ruleId": "1861", "severity": 1, "message": "2227", "line": 11, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 11, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2232", "line": 12, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 12, "endColumn": 9}, {"ruleId": "1861", "severity": 1, "message": "2556", "line": 19, "column": 11, "nodeType": "1863", "messageId": "1864", "endLine": 19, "endColumn": 32}, {"ruleId": "1861", "severity": 1, "message": "2264", "line": 35, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 35, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2557", "line": 37, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 37, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "2364", "line": 38, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 38, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2215", "line": 39, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 39, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2558", "line": 40, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 40, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2133", "line": 42, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 42, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2138", "line": 48, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 48, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2559", "line": 55, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 55, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2560", "line": 56, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 56, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2561", "line": 57, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 57, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2562", "line": 86, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 86, "endColumn": 31}, {"ruleId": "1861", "severity": 1, "message": "2563", "line": 90, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 90, "endColumn": 31}, {"ruleId": "1861", "severity": 1, "message": "2564", "line": 95, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 95, "endColumn": 33}, {"ruleId": "1964", "severity": 1, "message": "2565", "line": 195, "column": 5, "nodeType": "1966", "endLine": 195, "endColumn": 30, "suggestions": "2566"}, {"ruleId": "1861", "severity": 1, "message": "2157", "line": 3, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 3, "endColumn": 5}, {"ruleId": "1861", "severity": 1, "message": "2158", "line": 4, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 12}, {"ruleId": "1861", "severity": 1, "message": "2228", "line": 9, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 9, "endColumn": 12}, {"ruleId": "1861", "severity": 1, "message": "2157", "line": 2, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2158", "line": 2, "column": 23, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 33}, {"ruleId": "1861", "severity": 1, "message": "2225", "line": 2, "column": 38, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 42}, {"ruleId": "1861", "severity": 1, "message": "2232", "line": 2, "column": 64, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 71}, {"ruleId": "1861", "severity": 1, "message": "2552", "line": 4, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 32}, {"ruleId": "1861", "severity": 1, "message": "2553", "line": 5, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 5, "endColumn": 30}, {"ruleId": "1861", "severity": 1, "message": "2567", "line": 9, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 9, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2568", "line": 9, "column": 23, "nodeType": "1863", "messageId": "1864", "endLine": 9, "endColumn": 30}, {"ruleId": "1861", "severity": 1, "message": "2554", "line": 9, "column": 32, "nodeType": "1863", "messageId": "1864", "endLine": 9, "endColumn": 41}, {"ruleId": "1861", "severity": 1, "message": "2569", "line": 9, "column": 43, "nodeType": "1863", "messageId": "1864", "endLine": 9, "endColumn": 51}, {"ruleId": "1861", "severity": 1, "message": "2570", "line": 9, "column": 53, "nodeType": "1863", "messageId": "1864", "endLine": 9, "endColumn": 63}, {"ruleId": "1861", "severity": 1, "message": "2571", "line": 9, "column": 65, "nodeType": "1863", "messageId": "1864", "endLine": 9, "endColumn": 77}, {"ruleId": "1861", "severity": 1, "message": "2572", "line": 9, "column": 79, "nodeType": "1863", "messageId": "1864", "endLine": 9, "endColumn": 90}, {"ruleId": "1861", "severity": 1, "message": "2573", "line": 9, "column": 92, "nodeType": "1863", "messageId": "1864", "endLine": 9, "endColumn": 102}, {"ruleId": "1861", "severity": 1, "message": "2574", "line": 9, "column": 104, "nodeType": "1863", "messageId": "1864", "endLine": 9, "endColumn": 116}, {"ruleId": "1861", "severity": 1, "message": "2575", "line": 9, "column": 118, "nodeType": "1863", "messageId": "1864", "endLine": 9, "endColumn": 129}, {"ruleId": "1861", "severity": 1, "message": "2576", "line": 9, "column": 131, "nodeType": "1863", "messageId": "1864", "endLine": 9, "endColumn": 140}, {"ruleId": "1861", "severity": 1, "message": "2577", "line": 15, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 15, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2247", "line": 16, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 16, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2578", "line": 17, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 17, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2372", "line": 18, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 18, "endColumn": 8}, {"ruleId": "1861", "severity": 1, "message": "2579", "line": 19, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 19, "endColumn": 11}, {"ruleId": "1861", "severity": 1, "message": "1970", "line": 20, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 20, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2371", "line": 23, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 23, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2580", "line": 24, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 24, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2581", "line": 25, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 25, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2582", "line": 26, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 26, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "2583", "line": 27, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 27, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "2584", "line": 28, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 28, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2585", "line": 29, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 29, "endColumn": 12}, {"ruleId": "1861", "severity": 1, "message": "2586", "line": 30, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 30, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2419", "line": 34, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 34, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2506", "line": 62, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 62, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2563", "line": 82, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 82, "endColumn": 31}, {"ruleId": "1861", "severity": 1, "message": "2587", "line": 83, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 83, "endColumn": 35}, {"ruleId": "1861", "severity": 1, "message": "2482", "line": 1, "column": 17, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "2225", "line": 3, "column": 38, "nodeType": "1863", "messageId": "1864", "endLine": 3, "endColumn": 42}, {"ruleId": "1861", "severity": 1, "message": "1893", "line": 3, "column": 64, "nodeType": "1863", "messageId": "1864", "endLine": 3, "endColumn": 78}, {"ruleId": "1861", "severity": 1, "message": "2227", "line": 3, "column": 80, "nodeType": "1863", "messageId": "1864", "endLine": 3, "endColumn": 91}, {"ruleId": "1861", "severity": 1, "message": "2228", "line": 3, "column": 93, "nodeType": "1863", "messageId": "1864", "endLine": 3, "endColumn": 103}, {"ruleId": "1861", "severity": 1, "message": "2229", "line": 3, "column": 105, "nodeType": "1863", "messageId": "1864", "endLine": 3, "endColumn": 111}, {"ruleId": "1861", "severity": 1, "message": "2230", "line": 3, "column": 113, "nodeType": "1863", "messageId": "1864", "endLine": 3, "endColumn": 121}, {"ruleId": "1861", "severity": 1, "message": "2483", "line": 3, "column": 123, "nodeType": "1863", "messageId": "1864", "endLine": 3, "endColumn": 140}, {"ruleId": "1861", "severity": 1, "message": "2117", "line": 3, "column": 142, "nodeType": "1863", "messageId": "1864", "endLine": 3, "endColumn": 158}, {"ruleId": "1861", "severity": 1, "message": "2484", "line": 3, "column": 160, "nodeType": "1863", "messageId": "1864", "endLine": 3, "endColumn": 166}, {"ruleId": "1861", "severity": 1, "message": "2487", "line": 5, "column": 26, "nodeType": "1863", "messageId": "1864", "endLine": 5, "endColumn": 49}, {"ruleId": "1861", "severity": 1, "message": "2488", "line": 5, "column": 51, "nodeType": "1863", "messageId": "1864", "endLine": 5, "endColumn": 71}, {"ruleId": "1861", "severity": 1, "message": "2489", "line": 5, "column": 73, "nodeType": "1863", "messageId": "1864", "endLine": 5, "endColumn": 91}, {"ruleId": "1861", "severity": 1, "message": "2490", "line": 6, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 6, "endColumn": 31}, {"ruleId": "1861", "severity": 1, "message": "2491", "line": 8, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 8, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2492", "line": 9, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 9, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2493", "line": 10, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 10, "endColumn": 11}, {"ruleId": "1861", "severity": 1, "message": "2494", "line": 11, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 11, "endColumn": 8}, {"ruleId": "1861", "severity": 1, "message": "2502", "line": 19, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 19, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2504", "line": 22, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 22, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2588", "line": 24, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 24, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2589", "line": 25, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 25, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2590", "line": 26, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 26, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2591", "line": 27, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 27, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2419", "line": 31, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 31, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2506", "line": 36, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 36, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2592", "line": 104, "column": 21, "nodeType": "1863", "messageId": "1864", "endLine": 104, "endColumn": 33}, {"ruleId": "1861", "severity": 1, "message": "2593", "line": 105, "column": 24, "nodeType": "1863", "messageId": "1864", "endLine": 105, "endColumn": 40}, {"ruleId": "1861", "severity": 1, "message": "2509", "line": 120, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 120, "endColumn": 31}, {"ruleId": "1861", "severity": 1, "message": "2510", "line": 154, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 154, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "2594", "line": 157, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 157, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2225", "line": 3, "column": 38, "nodeType": "1863", "messageId": "1864", "endLine": 3, "endColumn": 42}, {"ruleId": "1861", "severity": 1, "message": "2232", "line": 3, "column": 56, "nodeType": "1863", "messageId": "1864", "endLine": 3, "endColumn": 63}, {"ruleId": "1861", "severity": 1, "message": "2134", "line": 13, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 13, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2244", "line": 14, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 14, "endColumn": 10}, {"ruleId": "1861", "severity": 1, "message": "2245", "line": 15, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 15, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "1951", "line": 16, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 16, "endColumn": 11}, {"ruleId": "1861", "severity": 1, "message": "1953", "line": 18, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 18, "endColumn": 9}, {"ruleId": "1861", "severity": 1, "message": "1954", "line": 19, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 19, "endColumn": 12}, {"ruleId": "1861", "severity": 1, "message": "1955", "line": 20, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 20, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "1956", "line": 21, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 21, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "2246", "line": 22, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 22, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2247", "line": 23, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 23, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2248", "line": 24, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 24, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "1970", "line": 25, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 25, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2595", "line": 37, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 37, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2286", "line": 39, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 39, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2596", "line": 41, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 41, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2597", "line": 45, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 45, "endColumn": 28}, {"ruleId": "1861", "severity": 1, "message": "2598", "line": 49, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 49, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2599", "line": 49, "column": 25, "nodeType": "1863", "messageId": "1864", "endLine": 49, "endColumn": 42}, {"ruleId": "1861", "severity": 1, "message": "2600", "line": 50, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 50, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2601", "line": 50, "column": 21, "nodeType": "1863", "messageId": "1864", "endLine": 50, "endColumn": 34}, {"ruleId": "1861", "severity": 1, "message": "2602", "line": 51, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 51, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2603", "line": 51, "column": 26, "nodeType": "1863", "messageId": "1864", "endLine": 51, "endColumn": 44}, {"ruleId": "1861", "severity": 1, "message": "2604", "line": 52, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 52, "endColumn": 28}, {"ruleId": "1861", "severity": 1, "message": "2605", "line": 52, "column": 30, "nodeType": "1863", "messageId": "1864", "endLine": 52, "endColumn": 52}, {"ruleId": "1861", "severity": 1, "message": "2606", "line": 53, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 53, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2607", "line": 53, "column": 27, "nodeType": "1863", "messageId": "1864", "endLine": 53, "endColumn": 46}, {"ruleId": "1861", "severity": 1, "message": "2608", "line": 3, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 3, "endColumn": 8}, {"ruleId": "1861", "severity": 1, "message": "2609", "line": 4, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2610", "line": 5, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 5, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2611", "line": 6, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 6, "endColumn": 10}, {"ruleId": "1861", "severity": 1, "message": "2404", "line": 9, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 9, "endColumn": 9}, {"ruleId": "1861", "severity": 1, "message": "2115", "line": 17, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 17, "endColumn": 12}, {"ruleId": "1861", "severity": 1, "message": "2116", "line": 18, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 18, "endColumn": 7}, {"ruleId": "1861", "severity": 1, "message": "2117", "line": 19, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 19, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2118", "line": 20, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 20, "endColumn": 7}, {"ruleId": "1861", "severity": 1, "message": "2119", "line": 23, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 23, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2120", "line": 24, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 24, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2121", "line": 25, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 25, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2232", "line": 26, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 26, "endColumn": 9}, {"ruleId": "1861", "severity": 1, "message": "2305", "line": 43, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 43, "endColumn": 35}, {"ruleId": "1861", "severity": 1, "message": "2612", "line": 44, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 44, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2613", "line": 50, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 50, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2614", "line": 51, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 51, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2615", "line": 53, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 53, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2616", "line": 54, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 54, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2617", "line": 55, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 55, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2618", "line": 56, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 56, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2134", "line": 58, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 58, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2167", "line": 59, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 59, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2433", "line": 60, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 60, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2128", "line": 62, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 62, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2129", "line": 68, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 68, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2130", "line": 69, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 69, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2131", "line": 75, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 75, "endColumn": 10}, {"ruleId": "1861", "severity": 1, "message": "2619", "line": 77, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 77, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "1980", "line": 80, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 80, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "1981", "line": 81, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 81, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "1979", "line": 82, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 82, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2620", "line": 83, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 83, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "2621", "line": 84, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 84, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2622", "line": 85, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 85, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2144", "line": 87, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 87, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2142", "line": 89, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 89, "endColumn": 11}, {"ruleId": "1861", "severity": 1, "message": "2146", "line": 91, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 91, "endColumn": 10}, {"ruleId": "1861", "severity": 1, "message": "2623", "line": 92, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 92, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2139", "line": 94, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 94, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2247", "line": 101, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 101, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2246", "line": 101, "column": 22, "nodeType": "1863", "messageId": "1864", "endLine": 101, "endColumn": 36}, {"ruleId": "1861", "severity": 1, "message": "1970", "line": 102, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 102, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2248", "line": 102, "column": 26, "nodeType": "1863", "messageId": "1864", "endLine": 102, "endColumn": 44}, {"ruleId": "1861", "severity": 1, "message": "2624", "line": 103, "column": 17, "nodeType": "1863", "messageId": "1864", "endLine": 103, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "2625", "line": 104, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 104, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2626", "line": 105, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 105, "endColumn": 12}, {"ruleId": "1861", "severity": 1, "message": "2627", "line": 105, "column": 14, "nodeType": "1863", "messageId": "1864", "endLine": 105, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2140", "line": 106, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 106, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2628", "line": 106, "column": 17, "nodeType": "1863", "messageId": "1864", "endLine": 106, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "2332", "line": 107, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 107, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2629", "line": 107, "column": 23, "nodeType": "1863", "messageId": "1864", "endLine": 107, "endColumn": 38}, {"ruleId": "1861", "severity": 1, "message": "2561", "line": 118, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 118, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2630", "line": 118, "column": 17, "nodeType": "1863", "messageId": "1864", "endLine": 118, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "2631", "line": 125, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 125, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2632", "line": 125, "column": 26, "nodeType": "1863", "messageId": "1864", "endLine": 125, "endColumn": 44}, {"ruleId": "1964", "severity": 1, "message": "2633", "line": 148, "column": 5, "nodeType": "1966", "endLine": 148, "endColumn": 60, "suggestions": "2634"}, {"ruleId": "1861", "severity": 1, "message": "2635", "line": 151, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 151, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2636", "line": 163, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 163, "endColumn": 24}, {"ruleId": "1964", "severity": 1, "message": "2637", "line": 167, "column": 5, "nodeType": "1966", "endLine": 167, "endColumn": 60, "suggestions": "2638"}, {"ruleId": "1861", "severity": 1, "message": "2639", "line": 169, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 169, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "2147", "line": 203, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 203, "endColumn": 30}, {"ruleId": "1861", "severity": 1, "message": "2148", "line": 207, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 207, "endColumn": 29}, {"ruleId": "1861", "severity": 1, "message": "2428", "line": 2, "column": 56, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 65}, {"ruleId": "1861", "severity": 1, "message": "2230", "line": 2, "column": 67, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 75}, {"ruleId": "1861", "severity": 1, "message": "2098", "line": 2, "column": 77, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 83}, {"ruleId": "1861", "severity": 1, "message": "2431", "line": 13, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 13, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2640", "line": 47, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 47, "endColumn": 48}, {"ruleId": "1861", "severity": 1, "message": "2328", "line": 59, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 59, "endColumn": 39}, {"ruleId": "1861", "severity": 1, "message": "2641", "line": 68, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 68, "endColumn": 41}, {"ruleId": "1861", "severity": 1, "message": "2642", "line": 74, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 74, "endColumn": 42}, {"ruleId": "1861", "severity": 1, "message": "2482", "line": 1, "column": 17, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "2352", "line": 1, "column": 38, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 47}, {"ruleId": "1861", "severity": 1, "message": "2225", "line": 2, "column": 38, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 42}, {"ruleId": "1861", "severity": 1, "message": "1893", "line": 2, "column": 64, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 78}, {"ruleId": "1861", "severity": 1, "message": "2228", "line": 2, "column": 93, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 103}, {"ruleId": "1861", "severity": 1, "message": "2483", "line": 2, "column": 123, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 140}, {"ruleId": "1861", "severity": 1, "message": "2117", "line": 2, "column": 142, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 158}, {"ruleId": "1861", "severity": 1, "message": "2484", "line": 2, "column": 160, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 166}, {"ruleId": "1861", "severity": 1, "message": "2487", "line": 4, "column": 26, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 49}, {"ruleId": "1861", "severity": 1, "message": "2488", "line": 4, "column": 51, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 71}, {"ruleId": "1861", "severity": 1, "message": "2489", "line": 4, "column": 73, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 91}, {"ruleId": "1861", "severity": 1, "message": "2490", "line": 5, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 5, "endColumn": 31}, {"ruleId": "1861", "severity": 1, "message": "2643", "line": 23, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 23, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "1989", "line": 24, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 24, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2644", "line": 26, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 26, "endColumn": 29}, {"ruleId": "1861", "severity": 1, "message": "1985", "line": 27, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 27, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2645", "line": 28, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 28, "endColumn": 29}, {"ruleId": "1861", "severity": 1, "message": "2646", "line": 29, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 29, "endColumn": 31}, {"ruleId": "1861", "severity": 1, "message": "2271", "line": 85, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 85, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2304", "line": 4, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "1980", "line": 11, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 11, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "1981", "line": 12, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 12, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "1979", "line": 13, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 13, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2310", "line": 21, "column": 21, "nodeType": "1863", "messageId": "1864", "endLine": 21, "endColumn": 34}, {"ruleId": "1861", "severity": 1, "message": "2311", "line": 22, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 22, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2312", "line": 22, "column": 33, "nodeType": "1863", "messageId": "1864", "endLine": 22, "endColumn": 44}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 82, "column": 22, "nodeType": "2016", "messageId": "2017", "endLine": 82, "endColumn": 24}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 82, "column": 53, "nodeType": "2016", "messageId": "2017", "endLine": 82, "endColumn": 55}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 85, "column": 36, "nodeType": "2016", "messageId": "2017", "endLine": 85, "endColumn": 38}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 85, "column": 63, "nodeType": "2016", "messageId": "2017", "endLine": 85, "endColumn": 65}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 88, "column": 36, "nodeType": "2016", "messageId": "2017", "endLine": 88, "endColumn": 38}, {"ruleId": "1861", "severity": 1, "message": "2314", "line": 95, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 95, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2315", "line": 96, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 96, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2316", "line": 99, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 99, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2317", "line": 100, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 100, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2318", "line": 108, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 108, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2319", "line": 128, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 128, "endColumn": 16}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 268, "column": 45, "nodeType": "2016", "messageId": "2017", "endLine": 268, "endColumn": 47}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 268, "column": 104, "nodeType": "2016", "messageId": "2017", "endLine": 268, "endColumn": 106}, {"ruleId": "1861", "severity": 1, "message": "2647", "line": 1, "column": 17, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "2360", "line": 4, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2428", "line": 8, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 8, "endColumn": 11}, {"ruleId": "1861", "severity": 1, "message": "2648", "line": 15, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 15, "endColumn": 28}, {"ruleId": "1861", "severity": 1, "message": "2649", "line": 15, "column": 30, "nodeType": "1863", "messageId": "1864", "endLine": 15, "endColumn": 44}, {"ruleId": "1861", "severity": 1, "message": "2650", "line": 15, "column": 46, "nodeType": "1863", "messageId": "1864", "endLine": 15, "endColumn": 60}, {"ruleId": "1861", "severity": 1, "message": "2651", "line": 15, "column": 62, "nodeType": "1863", "messageId": "1864", "endLine": 15, "endColumn": 78}, {"ruleId": "1861", "severity": 1, "message": "2652", "line": 15, "column": 80, "nodeType": "1863", "messageId": "1864", "endLine": 15, "endColumn": 96}, {"ruleId": "1861", "severity": 1, "message": "2653", "line": 17, "column": 29, "nodeType": "1863", "messageId": "1864", "endLine": 17, "endColumn": 33}, {"ruleId": "1861", "severity": 1, "message": "2654", "line": 17, "column": 35, "nodeType": "1863", "messageId": "1864", "endLine": 17, "endColumn": 47}, {"ruleId": "1861", "severity": 1, "message": "2123", "line": 17, "column": 49, "nodeType": "1863", "messageId": "1864", "endLine": 17, "endColumn": 55}, {"ruleId": "1861", "severity": 1, "message": "2655", "line": 22, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 22, "endColumn": 11}, {"ruleId": "1861", "severity": 1, "message": "2293", "line": 58, "column": 13, "nodeType": "1863", "messageId": "1864", "endLine": 58, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2656", "line": 66, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 66, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2372", "line": 72, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 72, "endColumn": 8}, {"ruleId": "1861", "severity": 1, "message": "2371", "line": 73, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 73, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2581", "line": 74, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 74, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2247", "line": 75, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 75, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2657", "line": 76, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 76, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2379", "line": 77, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 77, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2658", "line": 78, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 78, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2380", "line": 79, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 79, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2375", "line": 80, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 80, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2659", "line": 81, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 81, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2583", "line": 82, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 82, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "2132", "line": 83, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 83, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2127", "line": 84, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 84, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2660", "line": 87, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 87, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2661", "line": 89, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 89, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2133", "line": 91, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 91, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2138", "line": 93, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 93, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2662", "line": 183, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 183, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2663", "line": 186, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 186, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2664", "line": 196, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 196, "endColumn": 21}, {"ruleId": "1964", "severity": 1, "message": "2665", "line": 349, "column": 5, "nodeType": "1966", "endLine": 349, "endColumn": 18, "suggestions": "2666"}, {"ruleId": "1861", "severity": 1, "message": "2382", "line": 617, "column": 33, "nodeType": "1863", "messageId": "1864", "endLine": 617, "endColumn": 47}, {"ruleId": "1861", "severity": 1, "message": "2215", "line": 617, "column": 49, "nodeType": "1863", "messageId": "1864", "endLine": 617, "endColumn": 60}, {"ruleId": "1861", "severity": 1, "message": "2169", "line": 617, "column": 62, "nodeType": "1863", "messageId": "1864", "endLine": 617, "endColumn": 67}, {"ruleId": "1861", "severity": 1, "message": "2132", "line": 617, "column": 69, "nodeType": "1863", "messageId": "1864", "endLine": 617, "endColumn": 85}, {"ruleId": "1964", "severity": 1, "message": "2667", "line": 945, "column": 3, "nodeType": "1966", "endLine": 945, "endColumn": 19, "suggestions": "2668"}, {"ruleId": "1861", "severity": 1, "message": "2669", "line": 1017, "column": 15, "nodeType": "1863", "messageId": "1864", "endLine": 1017, "endColumn": 33}, {"ruleId": "1861", "severity": 1, "message": "2670", "line": 1026, "column": 15, "nodeType": "1863", "messageId": "1864", "endLine": 1026, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "2362", "line": 11, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 11, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2304", "line": 14, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 14, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "2671", "line": 67, "column": 11, "nodeType": "1863", "messageId": "1864", "endLine": 67, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2672", "line": 180, "column": 86, "nodeType": "1863", "messageId": "1864", "endLine": 180, "endColumn": 101}, {"ruleId": "1861", "severity": 1, "message": "2380", "line": 184, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 184, "endColumn": 24}, {"ruleId": "1964", "severity": 1, "message": "2673", "line": 563, "column": 5, "nodeType": "1966", "endLine": 563, "endColumn": 40, "suggestions": "2674"}, {"ruleId": "1964", "severity": 1, "message": "2675", "line": 586, "column": 6, "nodeType": "1966", "endLine": 586, "endColumn": 42, "suggestions": "2676"}, {"ruleId": "1964", "severity": 1, "message": "2677", "line": 600, "column": 6, "nodeType": "1966", "endLine": 600, "endColumn": 50, "suggestions": "2678"}, {"ruleId": "1964", "severity": 1, "message": "2679", "line": 877, "column": 5, "nodeType": "1966", "endLine": 877, "endColumn": 160, "suggestions": "2680"}, {"ruleId": "1964", "severity": 1, "message": "2681", "line": 945, "column": 5, "nodeType": "1966", "endLine": 945, "endColumn": 110, "suggestions": "2682"}, {"ruleId": "1964", "severity": 1, "message": "2683", "line": 975, "column": 5, "nodeType": "1966", "endLine": 975, "endColumn": 34, "suggestions": "2684"}, {"ruleId": "1964", "severity": 1, "message": "2685", "line": 993, "column": 5, "nodeType": "1966", "endLine": 993, "endColumn": 34, "suggestions": "2686"}, {"ruleId": "1964", "severity": 1, "message": "2685", "line": 1007, "column": 5, "nodeType": "1966", "endLine": 1007, "endColumn": 34, "suggestions": "2687"}, {"ruleId": "1964", "severity": 1, "message": "2685", "line": 1010, "column": 5, "nodeType": "1966", "endLine": 1010, "endColumn": 40, "suggestions": "2688"}, {"ruleId": "1861", "severity": 1, "message": "2689", "line": 1220, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 1220, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "2690", "line": 1223, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 1223, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2232", "line": 2, "column": 64, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 71}, {"ruleId": "1861", "severity": 1, "message": "2484", "line": 2, "column": 73, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 79}, {"ruleId": "1861", "severity": 1, "message": "1911", "line": 15, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 15, "endColumn": 9}, {"ruleId": "1861", "severity": 1, "message": "2691", "line": 18, "column": 48, "nodeType": "1863", "messageId": "1864", "endLine": 18, "endColumn": 76}, {"ruleId": "1861", "severity": 1, "message": "2692", "line": 18, "column": 78, "nodeType": "1863", "messageId": "1864", "endLine": 18, "endColumn": 85}, {"ruleId": "1861", "severity": 1, "message": "2237", "line": 20, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 20, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2693", "line": 52, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 52, "endColumn": 31}, {"ruleId": "1861", "severity": 1, "message": "2694", "line": 54, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 54, "endColumn": 31}, {"ruleId": "1861", "severity": 1, "message": "2695", "line": 59, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 59, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "2661", "line": 59, "column": 18, "nodeType": "1863", "messageId": "1864", "endLine": 59, "endColumn": 28}, {"ruleId": "1861", "severity": 1, "message": "2696", "line": 60, "column": 27, "nodeType": "1863", "messageId": "1864", "endLine": 60, "endColumn": 46}, {"ruleId": "1861", "severity": 1, "message": "1946", "line": 61, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 61, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "1947", "line": 61, "column": 16, "nodeType": "1863", "messageId": "1864", "endLine": 61, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2639", "line": 85, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 85, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "2512", "line": 92, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 92, "endColumn": 25}, {"ruleId": "1964", "severity": 1, "message": "2697", "line": 183, "column": 5, "nodeType": "1966", "endLine": 183, "endColumn": 52, "suggestions": "2698"}, {"ruleId": "1964", "severity": 1, "message": "2020", "line": 183, "column": 6, "nodeType": "2069", "endLine": 183, "endColumn": 51}, {"ruleId": "1861", "severity": 1, "message": "2158", "line": 2, "column": 92, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 102}, {"ruleId": "1861", "severity": 1, "message": "2699", "line": 76, "column": 19, "nodeType": "1863", "messageId": "1864", "endLine": 76, "endColumn": 30}, {"ruleId": "1861", "severity": 1, "message": "2404", "line": 2, "column": 27, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 34}, {"ruleId": "1861", "severity": 1, "message": "2231", "line": 2, "column": 36, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 46}, {"ruleId": "1861", "severity": 1, "message": "2428", "line": 2, "column": 48, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 57}, {"ruleId": "1861", "severity": 1, "message": "2230", "line": 2, "column": 59, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 67}, {"ruleId": "1861", "severity": 1, "message": "2098", "line": 2, "column": 69, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 75}, {"ruleId": "1861", "severity": 1, "message": "2232", "line": 2, "column": 77, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 84}, {"ruleId": "1861", "severity": 1, "message": "2700", "line": 3, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 3, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2701", "line": 4, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2702", "line": 8, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 8, "endColumn": 29}, {"ruleId": "1861", "severity": 1, "message": "2703", "line": 9, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 9, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2482", "line": 1, "column": 17, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "2401", "line": 1, "column": 29, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 37}, {"ruleId": "1861", "severity": 1, "message": "2352", "line": 1, "column": 38, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 47}, {"ruleId": "1861", "severity": 1, "message": "2223", "line": 1, "column": 49, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 59}, {"ruleId": "1861", "severity": 1, "message": "2359", "line": 1, "column": 61, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 67}, {"ruleId": "1861", "severity": 1, "message": "2428", "line": 2, "column": 27, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 36}, {"ruleId": "1861", "severity": 1, "message": "2225", "line": 2, "column": 38, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 42}, {"ruleId": "1861", "severity": 1, "message": "2098", "line": 2, "column": 56, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 62}, {"ruleId": "1861", "severity": 1, "message": "1893", "line": 2, "column": 64, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 78}, {"ruleId": "1861", "severity": 1, "message": "2227", "line": 2, "column": 80, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 91}, {"ruleId": "1861", "severity": 1, "message": "2228", "line": 2, "column": 93, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 103}, {"ruleId": "1861", "severity": 1, "message": "2229", "line": 2, "column": 105, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 111}, {"ruleId": "1861", "severity": 1, "message": "2230", "line": 2, "column": 113, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 121}, {"ruleId": "1861", "severity": 1, "message": "2483", "line": 2, "column": 123, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 140}, {"ruleId": "1861", "severity": 1, "message": "2117", "line": 2, "column": 142, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 158}, {"ruleId": "1861", "severity": 1, "message": "2484", "line": 2, "column": 160, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 166}, {"ruleId": "1861", "severity": 1, "message": "2405", "line": 3, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 3, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2190", "line": 4, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2487", "line": 4, "column": 26, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 49}, {"ruleId": "1861", "severity": 1, "message": "2488", "line": 4, "column": 51, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 71}, {"ruleId": "1861", "severity": 1, "message": "2489", "line": 4, "column": 73, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 91}, {"ruleId": "1861", "severity": 1, "message": "2490", "line": 5, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 5, "endColumn": 31}, {"ruleId": "1861", "severity": 1, "message": "2491", "line": 7, "column": 5, "nodeType": "1863", "messageId": "1864", "endLine": 7, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2492", "line": 8, "column": 5, "nodeType": "1863", "messageId": "1864", "endLine": 8, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2493", "line": 9, "column": 5, "nodeType": "1863", "messageId": "1864", "endLine": 9, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2494", "line": 10, "column": 7, "nodeType": "1863", "messageId": "1864", "endLine": 10, "endColumn": 12}, {"ruleId": "1861", "severity": 1, "message": "1891", "line": 11, "column": 7, "nodeType": "1863", "messageId": "1864", "endLine": 11, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2526", "line": 12, "column": 7, "nodeType": "1863", "messageId": "1864", "endLine": 12, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2704", "line": 19, "column": 7, "nodeType": "1863", "messageId": "1864", "endLine": 19, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2482", "line": 1, "column": 17, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "2225", "line": 2, "column": 38, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 42}, {"ruleId": "1861", "severity": 1, "message": "1893", "line": 2, "column": 64, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 78}, {"ruleId": "1861", "severity": 1, "message": "2228", "line": 2, "column": 93, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 103}, {"ruleId": "1861", "severity": 1, "message": "2483", "line": 2, "column": 123, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 140}, {"ruleId": "1861", "severity": 1, "message": "2117", "line": 2, "column": 142, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 158}, {"ruleId": "1861", "severity": 1, "message": "2484", "line": 2, "column": 160, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 166}, {"ruleId": "1861", "severity": 1, "message": "2120", "line": 2, "column": 177, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 193}, {"ruleId": "1861", "severity": 1, "message": "2487", "line": 4, "column": 26, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 49}, {"ruleId": "1861", "severity": 1, "message": "2488", "line": 4, "column": 51, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 71}, {"ruleId": "1861", "severity": 1, "message": "2489", "line": 4, "column": 73, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 91}, {"ruleId": "1861", "severity": 1, "message": "2490", "line": 5, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 5, "endColumn": 31}, {"ruleId": "1861", "severity": 1, "message": "2491", "line": 7, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 7, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2492", "line": 8, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 8, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2493", "line": 9, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 9, "endColumn": 11}, {"ruleId": "1861", "severity": 1, "message": "2494", "line": 10, "column": 5, "nodeType": "1863", "messageId": "1864", "endLine": 10, "endColumn": 10}, {"ruleId": "1861", "severity": 1, "message": "1891", "line": 11, "column": 5, "nodeType": "1863", "messageId": "1864", "endLine": 11, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2496", "line": 27, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 27, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2497", "line": 28, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 28, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2498", "line": 29, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 29, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2499", "line": 30, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 30, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2502", "line": 38, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 38, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2503", "line": 39, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 39, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2504", "line": 41, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 41, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2531", "line": 42, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 42, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2532", "line": 43, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 43, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2705", "line": 44, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 44, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2533", "line": 45, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 45, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2535", "line": 47, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 47, "endColumn": 30}, {"ruleId": "1861", "severity": 1, "message": "2536", "line": 48, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 48, "endColumn": 33}, {"ruleId": "1861", "severity": 1, "message": "2537", "line": 49, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 49, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2539", "line": 51, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 51, "endColumn": 30}, {"ruleId": "1861", "severity": 1, "message": "2540", "line": 52, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 52, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "2541", "line": 53, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 53, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2542", "line": 54, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 54, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2419", "line": 58, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 58, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2706", "line": 152, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 152, "endColumn": 40}, {"ruleId": "1861", "severity": 1, "message": "2707", "line": 153, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 153, "endColumn": 41}, {"ruleId": "1861", "severity": 1, "message": "2708", "line": 154, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 154, "endColumn": 47}, {"ruleId": "1861", "severity": 1, "message": "1946", "line": 156, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 156, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2709", "line": 185, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 185, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2510", "line": 220, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 220, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "2511", "line": 223, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 223, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2512", "line": 228, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 228, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "2054", "line": 245, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 245, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2545", "line": 249, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 249, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2551", "line": 254, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 254, "endColumn": 20}, {"ruleId": "1964", "severity": 1, "message": "2710", "line": 263, "column": 6, "nodeType": "1966", "endLine": 263, "endColumn": 8, "suggestions": "2711"}, {"ruleId": "1861", "severity": 1, "message": "2712", "line": 298, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 298, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2482", "line": 1, "column": 17, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "2359", "line": 1, "column": 49, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 55}, {"ruleId": "1861", "severity": 1, "message": "2713", "line": 1, "column": 69, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 80}, {"ruleId": "1861", "severity": 1, "message": "2225", "line": 2, "column": 38, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 42}, {"ruleId": "1861", "severity": 1, "message": "2228", "line": 2, "column": 93, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 103}, {"ruleId": "1861", "severity": 1, "message": "2483", "line": 2, "column": 123, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 140}, {"ruleId": "1861", "severity": 1, "message": "2117", "line": 2, "column": 142, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 158}, {"ruleId": "1861", "severity": 1, "message": "2484", "line": 2, "column": 160, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 166}, {"ruleId": "1861", "severity": 1, "message": "2360", "line": 2, "column": 195, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 212}, {"ruleId": "1861", "severity": 1, "message": "2487", "line": 5, "column": 26, "nodeType": "1863", "messageId": "1864", "endLine": 5, "endColumn": 49}, {"ruleId": "1861", "severity": 1, "message": "2488", "line": 5, "column": 51, "nodeType": "1863", "messageId": "1864", "endLine": 5, "endColumn": 71}, {"ruleId": "1861", "severity": 1, "message": "2489", "line": 5, "column": 73, "nodeType": "1863", "messageId": "1864", "endLine": 5, "endColumn": 91}, {"ruleId": "1861", "severity": 1, "message": "2490", "line": 6, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 6, "endColumn": 31}, {"ruleId": "1861", "severity": 1, "message": "2495", "line": 8, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 8, "endColumn": 28}, {"ruleId": "1861", "severity": 1, "message": "2496", "line": 9, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 9, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2497", "line": 10, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 10, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2498", "line": 11, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 11, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2499", "line": 12, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 12, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2500", "line": 13, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 13, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2491", "line": 15, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 15, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2492", "line": 16, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 16, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2493", "line": 17, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 17, "endColumn": 11}, {"ruleId": "1861", "severity": 1, "message": "2494", "line": 18, "column": 5, "nodeType": "1863", "messageId": "1864", "endLine": 18, "endColumn": 10}, {"ruleId": "1861", "severity": 1, "message": "1891", "line": 19, "column": 5, "nodeType": "1863", "messageId": "1864", "endLine": 19, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2701", "line": 34, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 34, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2714", "line": 44, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 44, "endColumn": 29}, {"ruleId": "1861", "severity": 1, "message": "2502", "line": 45, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 45, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2503", "line": 46, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 46, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2504", "line": 48, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 48, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2531", "line": 49, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 49, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2532", "line": 50, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 50, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2705", "line": 51, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 51, "endColumn": 28}, {"ruleId": "1861", "severity": 1, "message": "2533", "line": 52, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 52, "endColumn": 29}, {"ruleId": "1861", "severity": 1, "message": "2535", "line": 54, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 54, "endColumn": 35}, {"ruleId": "1861", "severity": 1, "message": "2536", "line": 55, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 55, "endColumn": 38}, {"ruleId": "1861", "severity": 1, "message": "2537", "line": 56, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 56, "endColumn": 28}, {"ruleId": "1861", "severity": 1, "message": "2539", "line": 58, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 58, "endColumn": 35}, {"ruleId": "1861", "severity": 1, "message": "2540", "line": 59, "column": 6, "nodeType": "1863", "messageId": "1864", "endLine": 59, "endColumn": 29}, {"ruleId": "1861", "severity": 1, "message": "2541", "line": 60, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 60, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "2542", "line": 61, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 61, "endColumn": 29}, {"ruleId": "1861", "severity": 1, "message": "2715", "line": 63, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 63, "endColumn": 29}, {"ruleId": "1861", "severity": 1, "message": "2419", "line": 66, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 66, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2706", "line": 89, "column": 11, "nodeType": "1863", "messageId": "1864", "endLine": 89, "endColumn": 42}, {"ruleId": "1861", "severity": 1, "message": "2707", "line": 90, "column": 11, "nodeType": "1863", "messageId": "1864", "endLine": 90, "endColumn": 43}, {"ruleId": "1861", "severity": 1, "message": "2708", "line": 91, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 91, "endColumn": 46}, {"ruleId": "1964", "severity": 1, "message": "2716", "line": 112, "column": 5, "nodeType": "1966", "endLine": 112, "endColumn": 52, "suggestions": "2717"}, {"ruleId": "1861", "severity": 1, "message": "2510", "line": 117, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 117, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "2511", "line": 120, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 120, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2512", "line": 125, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 125, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "2718", "line": 135, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 135, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2054", "line": 175, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 175, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2719", "line": 183, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 183, "endColumn": 20}, {"ruleId": "1964", "severity": 1, "message": "2710", "line": 188, "column": 4, "nodeType": "1966", "endLine": 188, "endColumn": 6, "suggestions": "2720"}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 193, "column": 19, "nodeType": "2016", "messageId": "2017", "endLine": 193, "endColumn": 21}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 194, "column": 19, "nodeType": "2016", "messageId": "2017", "endLine": 194, "endColumn": 21}, {"ruleId": "2014", "severity": 1, "message": "2015", "line": 194, "column": 39, "nodeType": "2016", "messageId": "2017", "endLine": 194, "endColumn": 41}, {"ruleId": "2014", "severity": 1, "message": "2015", "line": 213, "column": 19, "nodeType": "2016", "messageId": "2017", "endLine": 213, "endColumn": 21}, {"ruleId": "2014", "severity": 1, "message": "2036", "line": 226, "column": 20, "nodeType": "2016", "messageId": "2017", "endLine": 226, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2721", "line": 279, "column": 7, "nodeType": "1863", "messageId": "1864", "endLine": 279, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "1946", "line": 307, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 307, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2709", "line": 334, "column": 7, "nodeType": "1863", "messageId": "1864", "endLine": 334, "endColumn": 23}, {"ruleId": "1964", "severity": 1, "message": "2722", "line": 371, "column": 4, "nodeType": "1966", "endLine": 371, "endColumn": 6, "suggestions": "2723"}, {"ruleId": "1861", "severity": 1, "message": "2157", "line": 2, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2428", "line": 2, "column": 27, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 36}, {"ruleId": "1861", "severity": 1, "message": "2225", "line": 2, "column": 38, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 42}, {"ruleId": "1861", "severity": 1, "message": "2247", "line": 9, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 9, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2246", "line": 9, "column": 22, "nodeType": "1863", "messageId": "1864", "endLine": 9, "endColumn": 36}, {"ruleId": "1861", "severity": 1, "message": "1970", "line": 10, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 10, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2248", "line": 10, "column": 26, "nodeType": "1863", "messageId": "1864", "endLine": 10, "endColumn": 44}, {"ruleId": "1861", "severity": 1, "message": "2625", "line": 12, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 12, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2696", "line": 12, "column": 27, "nodeType": "1863", "messageId": "1864", "endLine": 12, "endColumn": 46}, {"ruleId": "1861", "severity": 1, "message": "2626", "line": 13, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 13, "endColumn": 12}, {"ruleId": "1861", "severity": 1, "message": "2627", "line": 13, "column": 14, "nodeType": "1863", "messageId": "1864", "endLine": 13, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2332", "line": 15, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 15, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2561", "line": 16, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 16, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2724", "line": 28, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 28, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2725", "line": 3, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 3, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2495", "line": 6, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 6, "endColumn": 28}, {"ruleId": "1861", "severity": 1, "message": "2158", "line": 2, "column": 32, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 42}, {"ruleId": "1861", "severity": 1, "message": "2428", "line": 2, "column": 44, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 53}, {"ruleId": "1861", "severity": 1, "message": "2431", "line": 4, "column": 46, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 65}, {"ruleId": "1861", "severity": 1, "message": "1891", "line": 4, "column": 67, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 75}, {"ruleId": "1861", "severity": 1, "message": "2726", "line": 8, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 8, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2727", "line": 31, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 31, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2261", "line": 33, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 33, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2728", "line": 43, "column": 6, "nodeType": "1863", "messageId": "1864", "endLine": 43, "endColumn": 34}, {"ruleId": "1861", "severity": 1, "message": "2729", "line": 85, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 85, "endColumn": 28}, {"ruleId": "1861", "severity": 1, "message": "2452", "line": 95, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 95, "endColumn": 26}, {"ruleId": "1861", "severity": 1, "message": "2702", "line": 5, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 5, "endColumn": 29}, {"ruleId": "1861", "severity": 1, "message": "2703", "line": 6, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 6, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2730", "line": 7, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 7, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2731", "line": 27, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 27, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2732", "line": 34, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 34, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2426", "line": 56, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 56, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2427", "line": 58, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 58, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2733", "line": 80, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 80, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2734", "line": 82, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 82, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2735", "line": 82, "column": 23, "nodeType": "1863", "messageId": "1864", "endLine": 82, "endColumn": 38}, {"ruleId": "1861", "severity": 1, "message": "2736", "line": 133, "column": 7, "nodeType": "1863", "messageId": "1864", "endLine": 133, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "2737", "line": 290, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 290, "endColumn": 28}, {"ruleId": "1964", "severity": 1, "message": "2738", "line": 344, "column": 5, "nodeType": "1966", "endLine": 344, "endColumn": 22, "suggestions": "2739"}, {"ruleId": "1861", "severity": 1, "message": "2647", "line": 1, "column": 58, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 67}, {"ruleId": "1861", "severity": 1, "message": "1867", "line": 1, "column": 75, "nodeType": "1863", "messageId": "1864", "endLine": 1, "endColumn": 82}, {"ruleId": "1861", "severity": 1, "message": "2404", "line": 2, "column": 15, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2158", "line": 2, "column": 33, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 43}, {"ruleId": "1861", "severity": 1, "message": "2655", "line": 4, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 4, "endColumn": 11}, {"ruleId": "1861", "severity": 1, "message": "2114", "line": 5, "column": 41, "nodeType": "1863", "messageId": "1864", "endLine": 5, "endColumn": 53}, {"ruleId": "1861", "severity": 1, "message": "2653", "line": 6, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 6, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2123", "line": 6, "column": 16, "nodeType": "1863", "messageId": "1864", "endLine": 6, "endColumn": 22}, {"ruleId": "1861", "severity": 1, "message": "2740", "line": 6, "column": 24, "nodeType": "1863", "messageId": "1864", "endLine": 6, "endColumn": 29}, {"ruleId": "1861", "severity": 1, "message": "2741", "line": 6, "column": 31, "nodeType": "1863", "messageId": "1864", "endLine": 6, "endColumn": 35}, {"ruleId": "1861", "severity": 1, "message": "2283", "line": 6, "column": 37, "nodeType": "1863", "messageId": "1864", "endLine": 6, "endColumn": 47}, {"ruleId": "1861", "severity": 1, "message": "2654", "line": 6, "column": 49, "nodeType": "1863", "messageId": "1864", "endLine": 6, "endColumn": 61}, {"ruleId": "1861", "severity": 1, "message": "2405", "line": 7, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 7, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2701", "line": 8, "column": 8, "nodeType": "1863", "messageId": "1864", "endLine": 8, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2742", "line": 42, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 42, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2743", "line": 43, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 43, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2744", "line": 45, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 45, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2290", "line": 46, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 46, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2745", "line": 47, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 47, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2289", "line": 48, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 48, "endColumn": 16}, {"ruleId": "1861", "severity": 1, "message": "2288", "line": 49, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 49, "endColumn": 15}, {"ruleId": "1861", "severity": 1, "message": "2746", "line": 50, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 50, "endColumn": 12}, {"ruleId": "1861", "severity": 1, "message": "2286", "line": 52, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 52, "endColumn": 19}, {"ruleId": "1861", "severity": 1, "message": "2747", "line": 53, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 53, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2748", "line": 54, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 54, "endColumn": 25}, {"ruleId": "1861", "severity": 1, "message": "2369", "line": 57, "column": 4, "nodeType": "1863", "messageId": "1864", "endLine": 57, "endColumn": 11}, {"ruleId": "1861", "severity": 1, "message": "2213", "line": 61, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 61, "endColumn": 18}, {"ruleId": "1861", "severity": 1, "message": "2214", "line": 61, "column": 20, "nodeType": "1863", "messageId": "1864", "endLine": 61, "endColumn": 31}, {"ruleId": "1861", "severity": 1, "message": "2749", "line": 63, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 63, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2750", "line": 63, "column": 26, "nodeType": "1863", "messageId": "1864", "endLine": 63, "endColumn": 43}, {"ruleId": "1861", "severity": 1, "message": "2751", "line": 80, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 80, "endColumn": 30}, {"ruleId": "1861", "severity": 1, "message": "2752", "line": 88, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 88, "endColumn": 29}, {"ruleId": "1964", "severity": 1, "message": "2753", "line": 131, "column": 6, "nodeType": "1966", "endLine": 131, "endColumn": 35, "suggestions": "2754"}, {"ruleId": "1861", "severity": 1, "message": "2119", "line": 2, "column": 2, "nodeType": "1863", "messageId": "1864", "endLine": 2, "endColumn": 14}, {"ruleId": "1861", "severity": 1, "message": "2755", "line": 20, "column": 24, "nodeType": "1863", "messageId": "1864", "endLine": 20, "endColumn": 35}, {"ruleId": "1861", "severity": 1, "message": "2258", "line": 42, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 42, "endColumn": 21}, {"ruleId": "1861", "severity": 1, "message": "2364", "line": 43, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 43, "endColumn": 23}, {"ruleId": "1861", "severity": 1, "message": "2135", "line": 44, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 44, "endColumn": 28}, {"ruleId": "1861", "severity": 1, "message": "2756", "line": 46, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 46, "endColumn": 13}, {"ruleId": "1861", "severity": 1, "message": "2216", "line": 48, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 48, "endColumn": 17}, {"ruleId": "1861", "severity": 1, "message": "2261", "line": 49, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 49, "endColumn": 20}, {"ruleId": "1861", "severity": 1, "message": "2242", "line": 60, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 60, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "2243", "line": 60, "column": 45, "nodeType": "1863", "messageId": "1864", "endLine": 60, "endColumn": 62}, {"ruleId": "1861", "severity": 1, "message": "2757", "line": 100, "column": 11, "nodeType": "1863", "messageId": "1864", "endLine": 100, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "2758", "line": 103, "column": 12, "nodeType": "1863", "messageId": "1864", "endLine": 103, "endColumn": 26}, {"ruleId": "1964", "severity": 1, "message": "2457", "line": 128, "column": 5, "nodeType": "1966", "endLine": 128, "endColumn": 155, "suggestions": "2759"}, {"ruleId": "1861", "severity": 1, "message": "2760", "line": 5, "column": 3, "nodeType": "1863", "messageId": "1864", "endLine": 5, "endColumn": 24}, {"ruleId": "1861", "severity": 1, "message": "2761", "line": 26, "column": 10, "nodeType": "1863", "messageId": "1864", "endLine": 26, "endColumn": 27}, {"ruleId": "1861", "severity": 1, "message": "2762", "line": 41, "column": 9, "nodeType": "1863", "messageId": "1864", "endLine": 41, "endColumn": 14}, "@typescript-eslint/no-unused-vars", "'GuidePopup' is defined but never used.", "Identifier", "unusedVar", "'Rte' is defined but never used.", "'LoginUserInfo' is defined but never used.", "'useMemo' is defined but never used.", "'Steps' is defined but never used.", "'PopupList' is defined but never used.", "'BUTTON_DEFAULT_VALUE' is defined but never used.", "'stopScraping' is defined but never used.", "'addicon' is defined but never used.", "'touricon' is defined but never used.", "'ProductToursicon' is defined but never used.", "'Tooltipsicon' is defined but never used.", "'announcementicon' is defined but never used.", "'Bannersicon' is defined but never used.", "'Checklisticon' is defined but never used.", "'Hotspoticon' is defined but never used.", "'Surveyicon' is defined but never used.", "'Announcementsicon' is defined but never used.", "'bannersicon' is defined but never used.", "'tooltipicon' is defined but never used.", "'checklisticon' is defined but never used.", "'hotspotsicon' is defined but never used.", "'surveysicon' is defined but never used.", "'settingsicon' is defined but never used.", "'undoicon' is defined but never used.", "'redoicon' is defined but never used.", "'shareicon' is defined but never used.", "'editicon' is defined but never used.", "'Outlet' is defined but never used.", "'InputAdornment' is defined but never used.", "'FormHelperText' is defined but never used.", "'List' is defined but never used.", "'Step' is defined but never used.", "'guideSetting' is defined but never used.", "'JSEncrypt' is defined but never used.", "'GetUserDetailsById' is defined but never used.", "'UserLogin' is defined but never used.", "'VisibilityOff' is defined but never used.", "'Visibility' is defined but never used.", "'initialsData' is defined but never used.", "'EditIcon' is defined but never used.", "'TooltipUserview' is defined but never used.", "'SubmitUpdateGuid' is defined but never used.", "'PageInteractions' is defined but never used.", "'ElementsSettings' is defined but never used.", "'DrawerState' is defined but never used.", "'Checklist' is defined but never used.", "'Padding' is defined but never used.", "'CheckIcon' is defined but never used.", "'TooltipPreview' is defined but never used.", "'DismissData' is defined but never used.", "'Canvas' is defined but never used.", "'Design' is defined but never used.", "'Advanced' is defined but never used.", "'Hotspot' is defined but never used.", "'stepId' is defined but never used.", "'userId' is defined but never used.", "'loginUserData' is defined but never used.", "'setIsGuidesListOpen' is assigned a value but never used.", "'setIsInHomeScreen' is assigned a value but never used.", "'setIsAnnouncementListOpen' is assigned a value but never used.", "'setIsBannerslistOpen' is assigned a value but never used.", "'selectedTemplated' is assigned a value but never used.", "'setSelectedTemplated' is assigned a value but never used.", "'errorInStepName' is assigned a value but never used.", "'showTextField' is assigned a value but never used.", "'signOut' is assigned a value but never used.", "'selectedElement' is assigned a value but never used.", "'setSelectedElement' is assigned a value but never used.", "'showPassword' is assigned a value but never used.", "'setShowPassword' is assigned a value but never used.", "'password' is assigned a value but never used.", "'setPassword' is assigned a value but never used.", "'loginUserInfo' is assigned a value but never used.", "'setLoginUserInfo' is assigned a value but never used.", "'setresponse' is assigned a value but never used.", "'isTooltipPopupOpen' is assigned a value but never used.", "'setIsTooltipPopupOpen' is assigned a value but never used.", "'email' is assigned a value but never used.", "'setEmail' is assigned a value but never used.", "'loginUserDetails' is assigned a value but never used.", "'setUserDetails' is assigned a value but never used.", "'error' is assigned a value but never used.", "'setError' is assigned a value but never used.", "'isSelectingElement' is assigned a value but never used.", "'selectedElementDetails' is assigned a value but never used.", "'setSelectedElementDetails' is assigned a value but never used.", "'position' is assigned a value but never used.", "'setPosition' is assigned a value but never used.", "'radius' is assigned a value but never used.", "'setRadius' is assigned a value but never used.", "'borderSize' is assigned a value but never used.", "'setBorderSize' is assigned a value but never used.", "'announcementData' is assigned a value but never used.", "'currentUrl' is assigned a value but never used.", "'isBannerPopupOpen' is assigned a value but never used.", "'i18nInitialized' is assigned a value but never used.", "'setI18nInitialized' is assigned a value but never used.", "'hashValue' is assigned a value but never used.", "'setHashValue' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has an unnecessary dependency: 'window.location.href'. Either exclude it or remove the dependency array. Outer scope values like 'window.location.href' aren't valid dependencies because mutating them doesn't re-render the component.", "ArrayExpression", ["2763"], "'fit' is assigned a value but never used.", "'fill' is assigned a value but never used.", "'backgroundColor' is assigned a value but never used.", "'sectionHeight' is assigned a value but never used.", "'setSectionHeight' is assigned a value but never used.", "'guidedatas' is assigned a value but never used.", "'setGuideDataS' is assigned a value but never used.", "'hotspotPopup' is assigned a value but never used.", "'setHotspotPopup' is assigned a value but never used.", "'textvaluess' is assigned a value but never used.", "'preview' is assigned a value but never used.", "'btnBorderColor' is assigned a value but never used.", "'btnBgColor' is assigned a value but never used.", "'btnTextColor' is assigned a value but never used.", "'isTooltipPopup' is assigned a value but never used.", "'setSteps' is assigned a value but never used.", "'newCurrentStep' is assigned a value but never used.", "'updateCanvasInTooltip' is assigned a value but never used.", "'hotspbgcolor' is assigned a value but never used.", "'setHotspBgColor' is assigned a value but never used.", "'setHotspotDataOnEdit' is assigned a value but never used.", "'openTooltip' is assigned a value but never used.", "'setXpathToTooltipMetaData' is assigned a value but never used.", "'setAxisData' is assigned a value but never used.", "'axisData' is assigned a value but never used.", "'setAutoPosition' is assigned a value but never used.", "'targetURL' is assigned a value but never used.", "'elementButtonName' is assigned a value but never used.", "'setElementButtonName' is assigned a value but never used.", "'isSaveClicked' is assigned a value but never used.", "'setbtnidss' is assigned a value but never used.", "'setPulseAnimationsH' is assigned a value but never used.", "'tooltipCount' is assigned a value but never used.", "'HotspotGuideDetails' is assigned a value but never used.", "'TooltipGuideDetailsNew' is assigned a value but never used.", "'editClicked' is assigned a value but never used.", "'textArray' is assigned a value but never used.", "'setTextArray' is assigned a value but never used.", "'setIsALTKeywordEnabled' is assigned a value but never used.", "'setDrawerActiveMenu' is assigned a value but never used.", "'setDrawerSearchText' is assigned a value but never used.", "'setInteractionData' is assigned a value but never used.", "'syncAIAnnouncementCanvasSettings' is assigned a value but never used.", "'ele4' is assigned a value but never used.", "'targetElement' is assigned a value but never used.", "'setHotspotClicked' is assigned a value but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "React Hook useEffect has missing dependencies: 'fetchGuideDetails' and 'hotspot'. Either include them or remove the dependency array.", ["2764"], "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "MemberExpression", "React Hook useEffect has a missing dependency: 'setDesignPopup'. Either include it or remove the dependency array.", ["2765"], "'screenWidth' is assigned a value but never used.", "'dialogWidth' is assigned a value but never used.", "'handlechangeStep' is assigned a value but never used.", "The 'initialState' object makes the dependencies of useEffect Hook (at line 1048) change on every render. To fix this, wrap the initialization of 'initialState' in its own useMemo() Hook.", "VariableDeclarator", "React Hook useEffect has a missing dependency: 'determineCurrentScreen'. Either include it or remove the dependency array.", ["2766"], "React Hook useEffect has a missing dependency: 'errors'. Either include it or remove the dependency array. Outer scope values like 'selectedStepType' aren't valid dependencies because mutating them doesn't re-render the component.", ["2767"], "React Hook useEffect has missing dependencies: 'bannerPopup', 'clearBannerButtonDetials', 'currentGuideId', 'selectedTemplate', 'selectedTemplateTour', 'setBannerButtonSelected', 'setBannerPopup', 'setIsAnnouncementCreationBuilderOpen', 'setIsBannerCreationBuilderOpen', 'setIsHotspotCreationBuilderOpen', 'setIsTooltipCreationBuilderOpen', 'updateButtonContainerOnReload', and 'updateRTEContainerOnReload'. Either include them or remove the dependency array.", ["2768"], "'setCount' is assigned a value but never used.", "Expected '===' and instead saw '=='.", "'handleGuidesSettingsclick' is assigned a value but never used.", "'synchronizePreviewData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'elementSelected', 'isShowIcon', 'setElementSelected', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["2769"], "'handleElementSelectionToggle' is assigned a value but never used.", "'userInfoObj' is assigned a value but never used.", "'isAnnouncementOpen' is assigned a value but never used.", "'setAnnouncementOpen' is assigned a value but never used.", "'aiCreationComplete' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setSettingAnchorEl'. Either include it or remove the dependency array.", ["2770"], "'defaultButtonSection' is assigned a value but never used.", "'responseData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'setIsUnSavedChanges' and 'stepCreation'. Either include them or remove the dependency array.", ["2771"], "'handleNewInteractionClick' is assigned a value but never used.", "Assignments to the 'accountId' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "'handleEditClick' is assigned a value but never used.", "'response' is assigned a value but never used.", "'editstepNameClicked' is assigned a value but never used.", "'setEditStepNameClicked' is assigned a value but never used.", "'handleNextClick' is assigned a value but never used.", "'isValid' is assigned a value but never used.", "'handleEventChange' is assigned a value but never used.", "'isGuideNameUnique' is assigned a value but never used.", "React Hook useEffect has an unnecessary dependency: 'updatedGuideData.GuideStep'. Either exclude it or remove the dependency array. Outer scope values like 'updatedGuideData.GuideStep' aren't valid dependencies because mutating them doesn't re-render the component.", ["2772"], "'getAlignment' is defined but never used.", "'popupVisible' is assigned a value but never used.", "'triggerType' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'currentGuide?.GuideStep'. Either include it or remove the dependency array.", ["2773"], "ChainExpression", "'customButton' is assigned a value but never used.", "'groupedButtons' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'cleanupDuplicateSteps', 'createWithAI', 'currentGuideId', 'interactionData', and 'resetHeightofBanner'. Either include them or remove the dependency array.", ["2774"], "'isDisabled' is assigned a value but never used.", "'guideType' is assigned a value but never used.", "'guideSteps' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'SetGuideName', 'cleanupDuplicateSteps', 'createWithAI', 'currentStep', 'getGuideTypeValue', 'handlecreatefromScratchclick', 'overlayEnabled', 'pageinteraction', 'progress', 'resetHeightofBanner', 'setBannerPopup', 'setBposition', 'setDismiss', 'setIsGuideInfoScreen', 'setOverlayEnabled', 'setPageInteraction', 'setProgress', 'setProgressColor', 'setSelectedOption', 'setSelectedTemplate', 'setSelectedTemplateTour', 'setTooltipCount', and 'setTourDataOnEdit'. Either include them or remove the dependency array.", ["2775"], "React Hook useEffect has missing dependencies: 'SetGuideName', 'currentGuideId', 'currentStep', 'getGuideTypeValue', 'handlecreatefromScratchclick', 'selectedTemplate', 'setIsGuideInfoScreen', 'setSelectedTemplate', and 'steps'. Either include them or remove the dependency array.", ["2776"], "React Hook useEffect has missing dependencies: 'setBannerPopup', 'setCurrentGuideId', 'setIsAnnouncementCreationBuilderOpen', 'setIsBannerCreationBuilderOpen', 'setIsGuideInfoScreen', 'setIsHomeScreen', 'setIsHotspotCreationBuilderOpen', 'setIsTemplateScreen', 'setIsTooltipCreationBuilderOpen', and 'setIsTooltipPopup'. Either include them or remove the dependency array.", ["2777"], "no-useless-escape", "Unnecessary escape character: \\/.", "Literal", "unnecessaryEscape", ["2778", "2779"], "'selectedStepTitle' is assigned a value but never used.", "'UserManager' is defined but never used.", "'useNavigate' is defined but never used.", "'useLocation' is defined but never used.", "'redirectPath' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loggedOut'. Either include it or remove the dependency array.", ["2780"], "'signIn' is assigned a value but never used.", "'CelebrationOutlinedIcon' is defined but never used.", "'ErrorOutlineOutlinedIcon' is defined but never used.", "'Button' is defined but never used.", "'Routes' is defined but never used.", "'RouteSharp' is defined but never used.", "'extractStateForHistory' is defined but never used.", "no-dupe-keys", "Duplicate key 'hotspotXaxis'.", "ObjectExpression", "Duplicate key 'setHotspotXaxis'.", "Duplicate key 'setSelectedTemplate'.", "Duplicate key 'toolTipGuideMetaData'.", "'isTourBanner' is assigned a value but never used.", "Duplicate key 'announcementGuideMetaData'.", "'opt' is assigned a value but never used.", "'targetStep' is assigned a value but never used.", "'future' is assigned a value but never used.", "'FALSE' is defined but never used.", "'TSectionType' is defined but never used.", "'RadioGroup' is defined but never used.", "'Radio' is defined but never used.", "'FormControlLabel' is defined but never used.", "'Input' is defined but never used.", "'Autocomplete' is defined but never used.", "'CircularProgress' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogActions' is defined but never used.", "'GifBox' is defined but never used.", "'Opacity' is defined but never used.", "'WarningIcon' is defined but never used.", "'color' is defined but never used.", "'dismissData' is assigned a value but never used.", "'setSelectActions' is assigned a value but never used.", "'setSelectedInteraction' is assigned a value but never used.", "'openInteractionList' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'currentStepIndex' is assigned a value but never used.", "'setProgress' is assigned a value but never used.", "'selectedTemplate' is assigned a value but never used.", "'updateTooltipButtonAction' is assigned a value but never used.", "'updateTooltipButtonInteraction' is assigned a value but never used.", "'selectedTemplateTour' is assigned a value but never used.", "'setProgressColor' is assigned a value but never used.", "'createWithAI' is assigned a value but never used.", "'action' is assigned a value but never used.", "'designPopup' is assigned a value but never used.", "'buttonId' is assigned a value but never used.", "'setButtonId' is assigned a value but never used.", "'cuntainerId' is assigned a value but never used.", "'setCuntainerId' is assigned a value but never used.", "'btnname' is assigned a value but never used.", "'handleCloseInteraction' is assigned a value but never used.", "'handleOpenInteraction' is assigned a value but never used.", "'sideAddButtonStyle' is assigned a value but never used.", "React Hook useMemo has missing dependencies: 'getCurrentButtonInfo', 'setBtnName', and 'setCurrentButtonName'. Either include them or remove the dependency array.", ["2781"], "'selectedButton' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'defaultButtonColors.backgroundColor', 'defaultButtonColors.borderColor', 'defaultButtonColors.color', 'getCurrentButtonInfo', 'setTargetURL', and 'targetURL'. Either include them or remove the dependency array.", ["2782"], "React Hook useEffect has missing dependencies: 'selectedActions.value' and 'targetURL'. Either include them or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setSelectedActions' needs the current value of 'selectedActions.value'.", ["2783"], "'Box' is defined but never used.", "'Typography' is defined but never used.", "'useAuth' is defined but never used.", "'clearAccessToken' is assigned a value but never used.", "'userLocalData' is assigned a value but never used.", "'SAinitialsData' is assigned a value but never used.", "'userDetails' is defined but never used.", "'ai' is defined but never used.", "'EnableAIButton' is defined but never used.", "'IsOpenAIKeyEnabledForAccount' is defined but never used.", "'setSelectedTemplate' is assigned a value but never used.", "'setSelectedTemplateTour' is assigned a value but never used.", "'steps' is assigned a value but never used.", "'setTooltipCount' is assigned a value but never used.", "'SetGuideName' is assigned a value but never used.", "'setIsTooltipPopup' is assigned a value but never used.", "'setBannerPopup' is assigned a value but never used.", "'setElementSelected' is assigned a value but never used.", "'TooltipGuideDetails' is assigned a value but never used.", "'HotspotGuideDetailsNew' is assigned a value but never used.", "'setSelectedStepTypeHotspot' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'isExtensionClosed' and 'setIsExtensionClosed'. Either include them or remove the dependency array.", ["2784"], "React Hook useEffect has missing dependencies: 'setDrawerActiveMenu', 'setDrawerSearchText', and 'setHasAnnouncementOpened'. Either include them or remove the dependency array.", ["2785"], "React Hook useEffect has missing dependencies: 'setDrawerActiveMenu', 'setDrawerSearchText', and 'setIsPopupOpen'. Either include them or remove the dependency array. If 'setIsPopupOpen' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2786"], "'handleEnableAI' is assigned a value but never used.", "'addPersistentHighlight' is assigned a value but never used.", "'showClickFeedback' is assigned a value but never used.", "'errorMessage' is assigned a value but never used.", "'axios' is defined but never used.", "'AnyMxRecord' is defined but never used.", "'useDrawerStore' is defined but never used.", "'ChecklistPopup' is defined but never used.", "'closeicon' is defined but never used.", "'closepluginicon' is defined but never used.", "'setShowLauncherSettings' is assigned a value but never used.", "'showLauncherSettings' is assigned a value but never used.", "'setIcons' is assigned a value but never used.", "'checklistColor' is assigned a value but never used.", "'GetGudeDetailsByGuideId' is defined but never used.", "'initialCompletedStatus' is assigned a value but never used.", "The 'checkpointslistData' logical expression could make the dependencies of useEffect Hook (at line 143) change on every render. To fix this, wrap the initialization of 'checkpointslistData' in its own useMemo() Hook.", "The 'checkpointslistData' logical expression could make the dependencies of useEffect Hook (at line 207) change on every render. To fix this, wrap the initialization of 'checkpointslistData' in its own useMemo() Hook.", "'checklistItems' is assigned a value but never used.", "'setChecklistItems' is assigned a value but never used.", "no-lone-blocks", "Nested block is redundant.", "BlockStatement", "redundantNestedBlock", "React Hook useEffect has a missing dependency: 'checkpointslistData'. Either include it or remove the dependency array.", ["2787"], "'iconColor' is assigned a value but never used.", "'base64IconFinal' is assigned a value but never used.", "'handleNavigate' is assigned a value but never used.", "'anchorEl' is assigned a value but never used.", "'setAnchorEl' is assigned a value but never used.", "'currentStep' is assigned a value but never used.", "'setCurrentStep' is assigned a value but never used.", "'scrollPercentage' is assigned a value but never used.", "'setScrollPercentage' is assigned a value but never used.", "'UndoIcon' is defined but never used.", "'RedoIcon' is defined but never used.", "'canUndoValue' is assigned a value but never used.", "'canRedoValue' is assigned a value but never used.", "'useContext' is defined but never used.", "'AccountContext' is defined but never used.", "'Grid' is defined but never used.", "'Container' is defined but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'IconButton' is defined but never used.", "'Tooltip' is defined but never used.", "'Alert' is defined but never used.", "'Chip' is defined but never used.", "'ViewModuleIcon' is defined but never used.", "'CodeIcon' is defined but never used.", "'TouchAppSharp' is defined but never used.", "'reselectElement' is assigned a value but never used.", "'setReselectElement' is assigned a value but never used.", "'goToNextElement' is assigned a value but never used.", "'setGoToNextElement' is assigned a value but never used.", "'setCurrentGuideId' is assigned a value but never used.", "'getCurrentGuideId' is assigned a value but never used.", "'padding' is assigned a value but never used.", "'setPadding' is assigned a value but never used.", "'setBorderColor' is assigned a value but never used.", "'borderColor' is assigned a value but never used.", "'setBackgroundColor' is assigned a value but never used.", "'overlayEnabled' is assigned a value but never used.", "'setZiindex' is assigned a value but never used.", "'setguidesSettingspopup' is assigned a value but never used.", "'setTooltipBackgroundcolor' is assigned a value but never used.", "'setTooltipBordercolor' is assigned a value but never used.", "'setTooltipBordersize' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE' is assigned a value but never used.", "'savedGuideData' is assigned a value but never used.", "'ButtonsDropdown' is assigned a value but never used.", "'setButtonsDropdown' is assigned a value but never used.", "'elementSelected' is assigned a value but never used.", "'elementbuttonClick' is assigned a value but never used.", "'highlightedButton' is assigned a value but never used.", "'mapButtonSection' is assigned a value but never used.", "'progress' is assigned a value but never used.", "'setSelectedOption' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'setHotspotPopup', 'setShowLauncherSettings', 'setShowTooltipCanvasSettings', and 'setTitlePopup'. Either include them or remove the dependency array.", ["2788"], "'toggleReselectElement' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'SetElementButtonClick', 'createWithAI', 'currentGuideId', 'interactionData', 'setButtonClick', 'setDropdownValue', 'setElementButtonName', 'setElementClick', and 'setbtnidss'. Either include them or remove the dependency array.", ["2789"], "'existingHotspot' is assigned a value but never used.", "'existingTooltip' is assigned a value but never used.", "'toggleCustomCSS' is assigned a value but never used.", "'toggleAnimation' is assigned a value but never used.", "'handleDismissDataChange' is assigned a value but never used.", "'setTooltipXaxis' is defined but never used.", "'setTooltipYaxis' is defined but never used.", "'setTooltipPosition' is defined but never used.", "'setTooltipBorderradius' is defined but never used.", "'setTooltipPadding' is defined but never used.", "'setTooltipWidth' is defined but never used.", "'updateCanvasInTooltip' is defined but never used.", "'setElementSelected' is defined but never used.", "'TextFormat' is defined but never used.", "'BUTTON_CONT_DEF_VALUE' is defined but never used.", "'saveGuide' is defined but never used.", "'setSectionColor' is assigned a value but never used.", "'setButtonProperty' is assigned a value but never used.", "'BborderSize' is assigned a value but never used.", "'Bbordercolor' is assigned a value but never used.", "'backgroundC' is assigned a value but never used.", "'setPreview' is assigned a value but never used.", "'clearGuideDetails' is assigned a value but never used.", "'translate' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'bannerButtonSelected', 'buttonColor', 'rtesContainer', 'setButtonColor', 'textAreas', and 'textBoxRef'. Either include them or remove the dependency array.", ["2790"], "React Hook useEffect has missing dependencies: 'buttonColor', 'removeTextArea', 'setButtonColor', and 'textAreas'. Either include them or remove the dependency array.", ["2791"], "React Hook useEffect has a missing dependency: 'setTextArray'. Either include it or remove the dependency array.", ["2792"], "React Hook useEffect has a missing dependency: 'textAreas'. Either include it or remove the dependency array.", ["2793"], "'setShowEmojiPicker' is assigned a value but never used.", "'enableProgress' is assigned a value but never used.", "'CustomIconButton' is defined but never used.", "'ArrowBackIosNewOutlinedIcon' is defined but never used.", "'parse' is defined but never used.", "'domToReact' is defined but never used.", "'Element' is defined but never used.", "'IconButtonSX' is defined but never used.", "'setShowBanner' is assigned a value but never used.", "'setImageSrc' is assigned a value but never used.", "'htmlContent' is assigned a value but never used.", "'Teext' is assigned a value but never used.", "'IconColor' is assigned a value but never used.", "'IconOpacity' is assigned a value but never used.", "'Width' is assigned a value but never used.", "'Radius' is assigned a value but never used.", "'Design' is assigned a value but never used.", "'brCount' is assigned a value but never used.", "'BannerEndUser' is defined but never used.", "'BannerStepPreview' is defined but never used.", "'setBannerPreview' is assigned a value but never used.", "'bannerPreview' is assigned a value but never used.", "'announcementPreview' is assigned a value but never used.", "'setAnnouncementPreview' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'OverlayValue'. Either include it or remove the dependency array. If 'setOverlayValue' needs the current value of 'OverlayValue', you can also switch to useReducer instead of useState and read 'OverlayValue' in the reducer.", ["2794"], "'imageStyle' is assigned a value but never used.", "'dissmissIconColor' is assigned a value but never used.", "'ActionButtonBackgroundcolor' is assigned a value but never used.", "'overlay' is assigned a value but never used.", "'openInNewTab' is assigned a value but never used.", "'HotspotGuideProps' is defined but never used.", "'hotspotGuideMetaData' is assigned a value but never used.", "valid-typeof", "Invalid typeof comparison value.", "invalidV<PERSON>ue", ["2795"], "React Hook useEffect has a missing dependency: 'getElementPosition'. Either include it or remove the dependency array.", ["2796"], "React Hook useEffect has a missing dependency: 'xpath'. Either include it or remove the dependency array.", ["2797"], "React Hook useEffect has a missing dependency: 'calculateOptimalWidth'. Either include it or remove the dependency array.", ["2798"], "React Hook useEffect has a missing dependency: 'guideStep'. Either include it or remove the dependency array.", ["2799"], "'hotspotData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'savedGuideData?.GuideStep', 'selectedTemplateTour', and 'setOpenTooltip'. Either include them or remove the dependency array.", ["2800"], ["2801"], "Assignments to the 'hotspot' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "'useEffect' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'count' is assigned a value but never used.", "'selectedStepStyle' is assigned a value but never used.", "'isSelected' is assigned a value but never used.", "'isHovered' is assigned a value but never used.", "'useRef' is defined but never used.", "'ClickAwayListener' is defined but never used.", "'LinearProgress' is defined but never used.", "'Breadcrumbs' is defined but never used.", "'updateCacheWithNewRows' is defined but never used.", "'toolTipGuideMetaData' is assigned a value but never used.", "no-unreachable", "Unreachable code.", "ReturnStatement", "unreachableCode", "'tooltip' is assigned a value but never used.", "'guideName' is assigned a value but never used.", "'borderRadius' is assigned a value but never used.", "'width' is assigned a value but never used.", "'tooltipXaxis' is assigned a value but never used.", "'tooltipYaxis' is assigned a value but never used.", "'tooltipWidth' is assigned a value but never used.", "'setTooltipWidth' is assigned a value but never used.", "'setTooltipPadding' is assigned a value but never used.", "'setTooltipBorderradius' is assigned a value but never used.", "'tooltipbordersize' is assigned a value but never used.", "'tooltipPosition' is assigned a value but never used.", "'setTooltipPosition' is assigned a value but never used.", "'selectedOption' is assigned a value but never used.", "'setCurrentStepIndex' is assigned a value but never used.", "'HotspotSettings' is assigned a value but never used.", "'currentGuideId' is assigned a value but never used.", "'hoveredElement' is assigned a value but never used.", "'setHoveredElement' is assigned a value but never used.", "'overlayPosition' is assigned a value but never used.", "'setOverlayPosition' is assigned a value but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'currentElement'.", "ArrowFunctionExpression", "unsafeRefs", "'removeAppliedStyleOfEle' is assigned a value but never used.", "'isElementHover' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'applyHotspotProperties', 'createWithAI', 'isCollapsed', 'isGuideInfoScreen', 'isTooltipNameScreenOpen', 'rectData', 'selectedTemplate', 'selectedTemplateTour', 'setAxisData', 'setCurrentHoveredElement', 'setElementSelected', 'setOpenTooltip', 'setTooltip', 'setXpathToTooltipMetaData', and 'syncAITooltipContainerData'. Either include them or remove the dependency array.", ["2802"], "React Hook useEffect has missing dependencies: 'isALTKeywordEnabled', 'selectedTemplate', 'selectedTemplateTour', and 'setIsALTKeywordEnabled'. Either include them or remove the dependency array.", ["2803"], "'DotsStepper' is assigned a value but never used.", "'useState' is defined but never used.", "'ForkLeft' is defined but never used.", "'handleStepTypeChange' is assigned a value but never used.", "'Popover' is defined but never used.", "'CloseIcon' is defined but never used.", "'PopoverOrigin' is defined but never used.", "React Hook useEffect has missing dependencies: 'initializeTourHotspotMetadata', 'savedGuideData?.GuideStep', 'setAnnouncementPreview', 'setBannerPreview', 'setHotspotPreview', 'setOpenTooltip', and 'setTooltipPreview'. Either include them or remove the dependency array.", ["2804"], "'setCurrentUrl' is assigned a value but never used.", "Assignments to the 'savedGuideData' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "CallExpression", "'userApiService' is defined but never used.", "'AnySoaRecord' is defined but never used.", "'userUrl' is assigned a value but never used.", "'AxiosResponse' is defined but never used.", "'adminApiService' is defined but never used.", "'idsApiService' is defined but never used.", "'ArrowBackIosIcon' is defined but never used.", "'isUnSavedChanges' is assigned a value but never used.", "'openWarning' is assigned a value but never used.", "'setName' is assigned a value but never used.", "'handleKeyDown' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAnnouncements'. Either include it or remove the dependency array.", ["2805"], ["2806"], "'snackbarKey' is assigned a value but never used.", "'openSnackbar' is assigned a value but never used.", "'TextField' is defined but never used.", "'selectedtemp' is defined but never used.", "'isCurrentlyEditing' is assigned a value but never used.", "'backgroundcoloricon' is defined but never used.", "'ButtonSettings' is defined but never used.", "'buttonProperty' is assigned a value but never used.", "'isEditingPrevious' is assigned a value but never used.", "'isEditingContinue' is assigned a value but never used.", "'previousButtonText' is assigned a value but never used.", "'continueButtonText' is assigned a value but never used.", "'buttonText' is assigned a value but never used.", "'setButtonText' is assigned a value but never used.", "'buttonToEdit' is assigned a value but never used.", "'isDeleteIcon' is assigned a value but never used.", "'isEditingButton' is assigned a value but never used.", "'isEditing' is assigned a value but never used.", "'setIsEditing' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setButtonProperty'. Either include it or remove the dependency array.", ["2807"], "'handlePreviousTextChange' is assigned a value but never used.", "'handleContinueTextChange' is assigned a value but never used.", "'toggleEdit' is assigned a value but never used.", "'handlePreviousBlur' is assigned a value but never used.", "'handleContinueBlur' is assigned a value but never used.", "'handleChangeButton' is assigned a value but never used.", "'handleEditButtonText' is assigned a value but never used.", "'LauncherSettings' is defined but never used.", "React Hook useEffect has missing dependencies: 'checkpointslistData' and 'completedStatus'. Either include them or remove the dependency array.", ["2808"], "React Hook useEffect has missing dependencies: 'createWithAI' and 'interactionData'. Either include them or remove the dependency array.", ["2809"], ["2810"], "React Hook useEffect has a missing dependency: 'checklistGuideMetaData'. Either include it or remove the dependency array.", ["2811"], "'toggleItemCompletion' is assigned a value but never used.", "'beta' is defined but never used.", "'setIsCollapsed' is assigned a value but never used.", "'handleClick' is assigned a value but never used.", "'micicon' is defined but never used.", "'micicon_hover' is defined but never used.", "'PerfectScrollbar' is defined but never used.", "'isChatOpen' is assigned a value but never used.", "'setIsChatOpen' is assigned a value but never used.", "'isMicHovered' is assigned a value but never used.", "'setIsMicHovered' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'accountId' and 'openSnackbar'. Either include them or remove the dependency array.", ["2812"], "'handleSpeechRecognition' is assigned a value but never used.", "'isTourCreationPrompt' is assigned a value but never used.", "'parseTourSteps' is assigned a value but never used.", "'dataNew' is assigned a value but never used.", "'stepDataNew' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setElementSelected'. Either include it or remove the dependency array.", ["2813"], "'useReducer' is defined but never used.", "'SelectChangeEvent' is defined but never used.", "'Switch' is defined but never used.", "'ToggleButton' is defined but never used.", "'ToggleButtonGroup' is defined but never used.", "'BUTTON_CONT_DEF_VALUE_1' is defined but never used.", "'CANVAS_DEFAULT_VALUE' is defined but never used.", "'IMG_CONT_DEF_VALUE' is defined but never used.", "'HOTSPOT_DEFAULT_VALUE' is defined but never used.", "'InfoFilled' is defined but never used.", "'QuestionFill' is defined but never used.", "'Reselect' is defined but never used.", "'Solid' is defined but never used.", "'AddCircleOutlineIcon' is defined but never used.", "'InsertPhotoIcon' is defined but never used.", "'PersonIcon' is defined but never used.", "'FavoriteIcon' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'ErrorOutlineIcon' is defined but never used.", "'position' is defined but never used.", "'titlePopup' is assigned a value but never used.", "'setTitlePopup' is assigned a value but never used.", "'titleColor' is assigned a value but never used.", "'launcherColor' is assigned a value but never used.", "'hasChanges' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistGuideMetaData', 'checklistLauncherProperties', and 'icons'. Either include them or remove the dependency array.", ["2814"], "'handleTitleColorChange' is assigned a value but never used.", "'handledesignclose' is assigned a value but never used.", "'handleSizeChange' is assigned a value but never used.", "'onReselectElement' is assigned a value but never used.", "'handleIconColorChange' is assigned a value but never used.", "'handleLauncherColorChange' is assigned a value but never used.", "'type' is assigned a value but never used.", "'text' is assigned a value but never used.", "'setText' is assigned a value but never used.", "'textColor' is assigned a value but never used.", "'setTextColor' is assigned a value but never used.", "'icon' is assigned a value but never used.", "'appliedIconColorBase64Icon' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistLauncherProperties', 'icons', and 'updateChecklistLauncher'. Either include them or remove the dependency array.", ["2815"], "'setPositionLeft' is assigned a value but never used.", "'setSetPositionLeft' is assigned a value but never used.", "'deleteicon' is defined but never used.", "'deletestep' is defined but never used.", "'editpricol' is defined but never used.", "'getAllGuides' is defined but never used.", "'ShowLauncherSettings' is assigned a value but never used.", "'setTitleColor' is assigned a value but never used.", "'checkpointsPopup' is assigned a value but never used.", "'checkpointTitleColor' is assigned a value but never used.", "'setCheckpointTitleColor' is assigned a value but never used.", "'checkpointTitleDescription' is assigned a value but never used.", "'setCheckpointTitleDescription' is assigned a value but never used.", "'checkpointIconColor' is assigned a value but never used.", "'setCheckpointIconColor' is assigned a value but never used.", "'setUnlockCheckPointInOrder' is assigned a value but never used.", "'unlockCheckPointInOrder' is assigned a value but never used.", "'checkPointMessage' is assigned a value but never used.", "'setCheckPointMessage' is assigned a value but never used.", "'accountId' is assigned a value but never used.", ["2816"], "'interactions' is assigned a value but never used.", "'setInteractions' is assigned a value but never used.", "'skip' is assigned a value but never used.", "'setSkip' is assigned a value but never used.", "'top' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "'dropdownRef' is assigned a value but never used.", "'RadioButtonUncheckedIcon' is defined but never used.", "'RadioButtonCheckedIcon' is defined but never used.", "'topCenter' is defined but never used.", "'OverlaySettingsProps' is defined but never used.", "'ElementsSettingsProps' is defined but never used.", "'setTooltipElementOptions' is assigned a value but never used.", "'updateprogressclick' is assigned a value but never used.", "'displayType' is assigned a value but never used.", "'dontShowAgain' is assigned a value but never used.", "'colors' is assigned a value but never used.", "'handleDisplayTypeChange' is assigned a value but never used.", "'handleBorderColorChange' is assigned a value but never used.", "'handleDontShowAgainChange' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dismissData.Color' and 'setDismiss'. Either include them or remove the dependency array.", ["2817"], "'defaultDots' is defined but never used.", "'topLeft' is defined but never used.", "'topRight' is defined but never used.", "'middleLeft' is defined but never used.", "'middleCenter' is defined but never used.", "'middleRight' is defined but never used.", "'bottomLeft' is defined but never used.", "'bottomMiddle' is defined but never used.", "'bottomRight' is defined but never used.", "'topcenter' is defined but never used.", "'setCanvasSetting' is assigned a value but never used.", "'announcement<PERSON>son' is assigned a value but never used.", "'setWidth' is assigned a value but never used.", "'setBorderRadius' is assigned a value but never used.", "'Annpadding' is assigned a value but never used.", "'setAnnPadding' is assigned a value but never used.", "'AnnborderSize' is assigned a value but never used.", "'setAnnBorderSize' is assigned a value but never used.", "'Bposition' is assigned a value but never used.", "'setBposition' is assigned a value but never used.", "'handleBackgroundColorChange' is assigned a value but never used.", "'checklistTitle' is assigned a value but never used.", "'setChecklistTitle' is assigned a value but never used.", "'checklistSubTitle' is assigned a value but never used.", "'setChecklistSubTitle' is assigned a value but never used.", "'setTempTitle' is assigned a value but never used.", "'settempTempTitle' is assigned a value but never used.", "'handleBlur' is assigned a value but never used.", "'setZindeex' is assigned a value but never used.", "'setOverlayEnabled' is assigned a value but never used.", "'handlePositionChange' is assigned a value but never used.", "'tempBorderSize' is assigned a value but never used.", "'setTempBorderSize' is assigned a value but never used.", "'tempZIndex' is assigned a value but never used.", "'setTempZIndex' is assigned a value but never used.", "'tempBorderColor' is assigned a value but never used.", "'setTempBorderColor' is assigned a value but never used.", "'tempBackgroundColor' is assigned a value but never used.", "'setTempBackgroundColor' is assigned a value but never used.", "'tempSectionColor' is assigned a value but never used.", "'setTempSectionColor' is assigned a value but never used.", "'Dialog' is defined but never used.", "'DialogContent' is defined but never used.", "'useMediaQuery' is defined but never used.", "'useTheme' is defined but never used.", "'zIndex' is defined but never used.", "'buttonsContainer' is assigned a value but never used.", "'cloneButtonContainer' is assigned a value but never used.", "'addNewButton' is assigned a value but never used.", "'deleteButton' is assigned a value but never used.", "'deleteButtonContainer' is assigned a value but never used.", "'updateContainer' is assigned a value but never used.", "'updateButtonInteraction' is assigned a value but never used.", "'setBtnBgColor' is assigned a value but never used.", "'setBtnTextColor' is assigned a value but never used.", "'setBtnBorderColor' is assigned a value but never used.", "'setBtnName' is assigned a value but never used.", "'setIsOpen' is assigned a value but never used.", "'selectedPosition' is assigned a value but never used.", "'url' is assigned a value but never used.", "'setUrl' is assigned a value but never used.", "'setAction' is assigned a value but never used.", "'setOpenInNewTab' is assigned a value but never used.", "'setColors' is assigned a value but never used.", "'buttonNameError' is assigned a value but never used.", "'setButtonNameError' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'defaultButtonColors.backgroundColor', 'defaultButtonColors.borderColor', 'defaultButtonColors.color', 'getCurrentButtonInfo', 'setCurrentButtonName', 'setSelectedTab', and 'setTargetURL'. Either include them or remove the dependency array.", ["2818"], "'positions' is assigned a value but never used.", "'curronButtonInfo' is assigned a value but never used.", "React Hook useMemo has missing dependencies: 'getCurrentButtonInfo' and 'setCurrentButtonName'. Either include them or remove the dependency array.", ["2819"], "'handlePositionClick' is assigned a value but never used.", "'imageContainerStyle' is assigned a value but never used.", "'iconRowStyle' is assigned a value but never used.", "'iconTextStyle' is assigned a value but never used.", "'setOpenTooltip' is assigned a value but never used.", "'setTooltipPositionByXpath' is assigned a value but never used.", "'updateTooltipBtnContainer' is assigned a value but never used.", "'updateTooltipImageContainer' is assigned a value but never used.", "'RefObject' is defined but never used.", "'CustomWidthTooltip' is defined but never used.", "'EXTENSION_PART' is defined but never used.", "'TOOLTIP_HEIGHT' is defined but never used.", "'TOOLTIP_MN_WIDTH' is defined but never used.", "'TOOLTIP_MX_WIDTH' is defined but never used.", "'Code' is defined but never used.", "'VideoLibrary' is defined but never used.", "'RTE' is defined but never used.", "'tooltipBackgroundcolor' is assigned a value but never used.", "'tooltipborderradius' is assigned a value but never used.", "'tooltipBordercolor' is assigned a value but never used.", "'tooltippadding' is assigned a value but never used.", "'elementClick' is assigned a value but never used.", "'setDismiss' is assigned a value but never used.", "'handleDragStart' is assigned a value but never used.", "'handleDragEnter' is assigned a value but never used.", "'handleDragEnd' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'popupPosition', 'setCurrentHoveredElement', 'setTooltipPositionByXpath', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["2820"], "React Hook useMemo has a missing dependency: 'handleFocus'. Either include it or remove the dependency array.", ["2821"], "'isInsideJoditPopup' is assigned a value but never used.", "'isPasteEvent' is assigned a value but never used.", "'CustomImage' is defined but never used.", "'pageinteraction' is assigned a value but never used.", "React Hook useCallback has an unnecessary dependency: 'smoothScrollTo'. Either exclude it or remove the dependency array.", ["2822"], "React Hook useEffect has missing dependencies: 'currentStep' and 'selectedTemplate'. Either include them or remove the dependency array.", ["2823"], "React Hook useEffect has missing dependencies: 'currentStep' and 'currentStepIndex'. Either include them or remove the dependency array.", ["2824"], "React Hook useCallback has unnecessary dependencies: 'calculateBestPosition' and 'scrollToTargetElement'. Either exclude them or remove the dependency array.", ["2825"], "React Hook useCallback has a missing dependency: 'steps'. Either include it or remove the dependency array.", ["2826"], "React Hook useEffect has a missing dependency: 'currentStepIndex'. Either include it or remove the dependency array.", ["2827"], "React Hook useEffect has a missing dependency: 'updateTargetAndPosition'. Either include it or remove the dependency array.", ["2828"], ["2829"], ["2830"], "'hasOnlyTextContent' is assigned a value but never used.", "'hasOnlyButton' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE_HOTSPOT' is defined but never used.", "'TCanvas' is defined but never used.", "'updateDesignelementInTooltip' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE_HOTSPOT' is assigned a value but never used.", "'dismiss' is assigned a value but never used.", "'setSelectedPosition' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'selectedTemplate', 'selectedTemplateTour', 'setTooltipBackgroundcolor', 'setTooltipBordercolor', 'setTooltipBorderradius', 'setTooltipBordersize', 'setTooltipPadding', 'setTooltipPosition', 'setTooltipWidth', 'setTooltipXaxis', 'setTooltipYaxis', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["2831"], "'guideStatus' is assigned a value but never used.", "'RemoveIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'DriveFolderUploadIcon' is defined but never used.", "'BackupIcon' is defined but never used.", "'modifySVGColor' is assigned a value but never used.", "'setCheckPointsPopup' is assigned a value but never used.", "'handleCheckPointIconColorChange' is assigned a value but never used.", "'handleCheckPointTitleColorChange' is assigned a value but never used.", "'handleCheckPointDescriptionColorChange' is assigned a value but never used.", "'handleFileUpload' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["2832"], "'handleMenuScroll' is assigned a value but never used.", "'useCallback' is defined but never used.", "'checkpointsEditPopup' is assigned a value but never used.", "'updateChecklistCheckPoints' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'filteredInteractions'. Either include it or remove the dependency array.", ["2833"], "'applyclicked' is assigned a value but never used.", "'isSearching' is assigned a value but never used.", ["2834"], "'handleSearch' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistCheckpointListProperties' and 'icons'. Either include them or remove the dependency array.", ["2835"], "'handleColorChange' is assigned a value but never used.", "'FolderIcon' is defined but never used.", "'useAsyncError' is defined but never used.", "'getCurrentButtonInfo' is assigned a value but never used.", "'clickTimeout' is defined but never used.", "'handleEditButtonName' is assigned a value but never used.", "'Modal' is defined but never used.", "'IMG_EXPONENT' is defined but never used.", "'getAllFiles' is defined but never used.", "'selectedColor' is assigned a value but never used.", "'formOfUpload' is assigned a value but never used.", "'setFormOfUpload' is assigned a value but never used.", "'urll' is defined but never used.", "'handleHyperlinkClick' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setImageAnchorEl'. Either include it or remove the dependency array.", ["2836"], "'Image' is defined but never used.", "'Link' is defined but never used.", "'setIsUnSavedChanges' is assigned a value but never used.", "'setHtmlContent' is assigned a value but never used.", "'setTextvaluess' is assigned a value but never used.", "'setBackgroundC' is assigned a value but never used.", "'bpadding' is assigned a value but never used.", "'handleTooltipRTEBlur' is assigned a value but never used.", "'handleTooltipRTEValue' is assigned a value but never used.", "'anchorPosition' is assigned a value but never used.", "'setAnchorPosition' is assigned a value but never used.", "'preserveCaretPosition' is assigned a value but never used.", "'restoreCaretPosition' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'boxRef' and 'textvaluess'. Either include them or remove the dependency array. Mutable values like 'boxRef.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["2837"], "'ColorResult' is defined but never used.", "'setBtnIdss' is assigned a value but never used.", "'gotoNextButtonId' is assigned a value but never used.", "'matchingButton' is assigned a value but never used.", ["2838"], "'getAvailableLanguages' is defined but never used.", "'getOrgLanguageKey' is defined but never used.", "'orgId' is assigned a value but never used.", {"desc": "2839", "fix": "2840"}, {"desc": "2841", "fix": "2842"}, {"desc": "2843", "fix": "2844"}, {"desc": "2845", "fix": "2846"}, {"desc": "2847", "fix": "2848"}, {"desc": "2849", "fix": "2850"}, {"desc": "2851", "fix": "2852"}, {"desc": "2853", "fix": "2854"}, {"desc": "2855", "fix": "2856"}, {"desc": "2857", "fix": "2858"}, {"desc": "2859", "fix": "2860"}, {"desc": "2861", "fix": "2862"}, {"desc": "2863", "fix": "2864"}, {"desc": "2865", "fix": "2866"}, {"desc": "2867", "fix": "2868"}, {"messageId": "2869", "fix": "2870", "desc": "2871"}, {"messageId": "2872", "fix": "2873", "desc": "2874"}, {"desc": "2875", "fix": "2876"}, {"desc": "2877", "fix": "2878"}, {"desc": "2879", "fix": "2880"}, {"desc": "2881", "fix": "2882"}, {"desc": "2883", "fix": "2884"}, {"desc": "2885", "fix": "2886"}, {"desc": "2887", "fix": "2888"}, {"desc": "2889", "fix": "2890"}, {"desc": "2891", "fix": "2892"}, {"desc": "2893", "fix": "2894"}, {"desc": "2895", "fix": "2896"}, {"desc": "2897", "fix": "2898"}, {"desc": "2899", "fix": "2900"}, {"desc": "2901", "fix": "2902"}, {"desc": "2903", "fix": "2904"}, {"messageId": "2905", "data": "2906", "fix": "2907", "desc": "2908"}, {"desc": "2909", "fix": "2910"}, {"desc": "2911", "fix": "2912"}, {"desc": "2913", "fix": "2914"}, {"desc": "2915", "fix": "2916"}, {"desc": "2917", "fix": "2918"}, {"desc": "2919", "fix": "2920"}, {"desc": "2921", "fix": "2922"}, {"desc": "2923", "fix": "2924"}, {"desc": "2925", "fix": "2926"}, {"desc": "2927", "fix": "2928"}, {"desc": "2929", "fix": "2930"}, {"desc": "2931", "fix": "2932"}, {"desc": "2933", "fix": "2934"}, {"desc": "2935", "fix": "2936"}, {"desc": "2889", "fix": "2937"}, {"desc": "2938", "fix": "2939"}, {"desc": "2940", "fix": "2941"}, {"desc": "2942", "fix": "2943"}, {"desc": "2944", "fix": "2945"}, {"desc": "2946", "fix": "2947"}, {"desc": "2938", "fix": "2948"}, {"desc": "2949", "fix": "2950"}, {"desc": "2951", "fix": "2952"}, {"desc": "2953", "fix": "2954"}, {"desc": "2955", "fix": "2956"}, {"desc": "2957", "fix": "2958"}, {"desc": "2959", "fix": "2960"}, {"desc": "2961", "fix": "2962"}, {"desc": "2963", "fix": "2964"}, {"desc": "2965", "fix": "2966"}, {"desc": "2967", "fix": "2968"}, {"desc": "2969", "fix": "2970"}, {"desc": "2971", "fix": "2972"}, {"desc": "2971", "fix": "2973"}, {"desc": "2974", "fix": "2975"}, {"desc": "2976", "fix": "2977"}, {"desc": "2978", "fix": "2979"}, {"desc": "2980", "fix": "2981"}, {"desc": "2978", "fix": "2982"}, {"desc": "2983", "fix": "2984"}, {"desc": "2985", "fix": "2986"}, {"desc": "2987", "fix": "2988"}, {"desc": "2989", "fix": "2990"}, "Update the dependencies array to be: []", {"range": "2991", "text": "2992"}, "Update the dependencies array to be: [fetchGuideDetails, hotspot, hotspotClicked]", {"range": "2993", "text": "2994"}, "Update the dependencies array to be: [designPopup, setDesignPopup]", {"range": "2995", "text": "2996"}, "Update the dependencies array to be: [isTemplateScreen, isPopupOpen, bannerPopup, currentGuideId, selectedTemplate, selectedTemplateTour, determineCurrentScreen]", {"range": "2997", "text": "2998"}, "Update the dependencies array to be: [isCollapsed, isGuideInfoScreen, isTemplateScreen, isPopupOpen, bannerPopup, currentScreen, selectedTemplate, selectedTemplateTour, guideName, currentGuideId, guideStep, htmlContent, imageSrc, buttonColor, imageName, errors]", {"range": "2999", "text": "3000"}, "Update the dependencies array to be: [savedGuideData, isSaveInProgress, currentGuideId, selectedTemplate, selectedTemplateTour, bannerPopup, setBannerPopup, setIsBannerCreationBuilderOpen, setIsAnnouncementCreationBuilderOpen, setIsTooltipCreationBuilderOpen, setIsHotspotCreationBuilderOpen, clearBannerButtonDetials, updateRTEContainerOnReload, setBannerButtonSelected, updateButtonContainerOnReload]", {"range": "3001", "text": "3002"}, "Update the dependencies array to be: [currentStep, elementSelected, handleClose, isShowIcon, setElementSelected, toolTipGuideMetaData]", {"range": "3003", "text": "3004"}, "Update the dependencies array to be: [openStepDropdown, plusIconclick, setSettingAnchorEl]", {"range": "3005", "text": "3006"}, "Update the dependencies array to be: [createWithAI, setIsUnSavedChanges, stepCreation]", {"range": "3007", "text": "3008"}, "Update the dependencies array to be: [isAnnouncementPopupOpen, showBannerenduser, showTooltipenduser, showHotspotenduser, isTourTemplate]", {"range": "3009", "text": "3010"}, "Update the dependencies array to be: [currentGuide?.GuideStep, currentStep]", {"range": "3011", "text": "3012"}, "Update the dependencies array to be: [cleanupDuplicateSteps, createWithAI, currentGuideId, elementSelected, interactionData, resetHeightofBanner]", {"range": "3013", "text": "3014"}, "Update the dependencies array to be: [SetGuideName, cleanupDuplicateSteps, createWithAI, currentGuideId, currentStep, getGuideTypeValue, handlecreatefromScratchclick, interactionData, overlayEnabled, pageinteraction, progress, resetHeightofBanner, setBannerPopup, setBposition, setDismiss, setIsGuideInfoScreen, setOverlayEnabled, setPageInteraction, setProgress, setProgressColor, setSelectedOption, setSelectedTemplate, setSelectedTemplateTour, setTooltipCount, setTourDataOnEdit]", {"range": "3015", "text": "3016"}, "Update the dependencies array to be: [isAnnouncementPopupOpen, showHotspotenduser, isHotspotPopupOpen, isTourTemplate, isChecklistPreview, currentGuideId, setIsGuideInfoScreen, setSelectedTemplate, handlecreatefromScratchclick, SetGuideName, steps, selectedTemplate, getGuideTypeValue, currentStep]", {"range": "3017", "text": "3018"}, "Update the dependencies array to be: [isLoggedIn, setBannerPopup, setCurrentGuideId, setIsAnnouncementCreationBuilderOpen, setIsBannerCreationBuilderOpen, setIsGuideInfoScreen, setIsHomeScreen, setIsHotspotCreationBuilderOpen, setIsTemplateScreen, setIsTooltipCreationBuilderOpen, setIsTooltipPopup]", {"range": "3019", "text": "3020"}, "removeEscape", {"range": "3021", "text": "3022"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "3023", "text": "3024"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", "Update the dependencies array to be: [loggedOut]", {"range": "3025", "text": "3026"}, "Update the dependencies array to be: [getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName, setBtnName]", {"range": "3027", "text": "3028"}, "Update the dependencies array to be: [settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, targetURL]", {"range": "3029", "text": "3030"}, "Update the dependencies array to be: [selectedActions.value, targetURL]", {"range": "3031", "text": "3032"}, "Update the dependencies array to be: [isExtensionClosed, setIsExtensionClosed]", {"range": "3033", "text": "3034"}, "Update the dependencies array to be: [hasAnnouncementOpened, setDrawerActiveMenu, setDrawerSearchText, setHasAnnouncementOpened]", {"range": "3035", "text": "3036"}, "Update the dependencies array to be: [isExtensionClosed, activeMenu, searchText, setDrawerActiveMenu, setDrawerSearchText, setIsPopupOpen]", {"range": "3037", "text": "3038"}, "Update the dependencies array to be: [checkpointslistData]", {"range": "3039", "text": "3040"}, "Update the dependencies array to be: [selectedTemplate, selectedTemplateTour, setHotspotPopup, setShowLauncherSettings, setShowTooltipCanvasSettings, setTitlePopup]", {"range": "3041", "text": "3042"}, "Update the dependencies array to be: [SetElementButtonClick, createWithAI, currentGuideId, currentStep, interactionData, setButtonClick, setDropdownValue, setElementButtonName, setElementClick, setbtnidss, toolTipGuideMetaData]", {"range": "3043", "text": "3044"}, "Update the dependencies array to be: [bannerButtonSelected, buttonColor, rtesContainer, setButtonColor, textAreas, textBoxRef]", {"range": "3045", "text": "3046"}, "Update the dependencies array to be: [bannerButtonSelected, buttonColor, removeTextArea, setButtonColor, textAreas]", {"range": "3047", "text": "3048"}, "Update the dependencies array to be: [setTextArray, textAreas]", {"range": "3049", "text": "3050"}, "Update the dependencies array to be: [createWithAI, bannerButtonSelected, buttonColor, setButtonColor, textAreas]", {"range": "3051", "text": "3052"}, "Update the dependencies array to be: [OverlayValue]", {"range": "3053", "text": "3054"}, "suggestString", {"type": "3055"}, {"range": "3056", "text": "3057"}, "Use `\"undefined\"` instead of `undefined`.", "Update the dependencies array to be: [getElementPosition, xpath]", {"range": "3058", "text": "3059"}, "Update the dependencies array to be: [savedGuideData, xpath]", {"range": "3060", "text": "3061"}, "Update the dependencies array to be: [textFieldProperties, imageProperties, customButton, currentStep, calculateOptimalWidth]", {"range": "3062", "text": "3063"}, "Update the dependencies array to be: [currentStep, guideStep, setOpenTooltip]", {"range": "3064", "text": "3065"}, "Update the dependencies array to be: [currentStep, isHotspotPopupOpen, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, toolTipGuideMetaData]", {"range": "3066", "text": "3067"}, "Update the dependencies array to be: [currentStep, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, showHotspotenduser, toolTipGuideMetaData]", {"range": "3068", "text": "3069"}, "Update the dependencies array to be: [toolTipGuideMetaData, elementSelected, borderSize, isALTKeywordEnabled, currentHoveredElement, rectData, selectedTemplate, selectedTemplateTour, isTooltipNameScreenOpen, isCollapsed, setCurrentHoveredElement, isGuideInfoScreen, setElementSelected, setAxisData, setTooltip, setOpenTooltip, setXpathToTooltipMetaData, createWithAI, syncAITooltipContainerData, applyHotspotProperties]", {"range": "3070", "text": "3071"}, "Update the dependencies array to be: [elementSelected, isALTKeywordEnabled, selectedTemplate, selectedTemplateTour, setIsALTKeywordEnabled]", {"range": "3072", "text": "3073"}, "Update the dependencies array to be: [stepType, currentStep, setAnnouncementPreview, setBannerPreview, setTooltipPreview, setHotspotPreview, savedGuideData?.GuideStep, setOpenTooltip, initializeTourHotspotMetadata]", {"range": "3074", "text": "3075"}, "Update the dependencies array to be: [paginationModel, activeTab, Open, accountId, fetchAnnouncements]", {"range": "3076", "text": "3077"}, "Update the dependencies array to be: [fetchAnnouncements, searchQuery]", {"range": "3078", "text": "3079"}, "Update the dependencies array to be: [setButtonProperty]", {"range": "3080", "text": "3081"}, "Update the dependencies array to be: [checkpointslistData, completedStatus]", {"range": "3082", "text": "3083"}, "Update the dependencies array to be: [selectedItem, activeItem, createWithAI, interactionData]", {"range": "3084", "text": "3085"}, {"range": "3086", "text": "3040"}, "Update the dependencies array to be: [checklistGuideMetaData]", {"range": "3087", "text": "3088"}, "Update the dependencies array to be: [accountId, openSnackbar]", {"range": "3089", "text": "3090"}, "Update the dependencies array to be: [setElementSelected]", {"range": "3091", "text": "3092"}, "Update the dependencies array to be: [checklistGuideMetaData, checklistLauncherProperties, icons]", {"range": "3093", "text": "3094"}, "Update the dependencies array to be: [checklistLauncherProperties, icons, updateChecklistLauncher]", {"range": "3095", "text": "3096"}, {"range": "3097", "text": "3088"}, "Update the dependencies array to be: [dismissData.Color, dismissData?.dismisssel, setDismiss]", {"range": "3098", "text": "3099"}, "Update the dependencies array to be: [settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setCurrentButtonName, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, setSelectedTab]", {"range": "3100", "text": "3101"}, "Update the dependencies array to be: [getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName]", {"range": "3102", "text": "3103"}, "Update the dependencies array to be: [currentStep, openTooltip, popupPosition, setCurrentHoveredElement, setTooltipPositionByXpath, toolTipGuideMetaData]", {"range": "3104", "text": "3105"}, "Update the dependencies array to be: [handleFocus, isRtlDirection]", {"range": "3106", "text": "3107"}, "Update the dependencies array to be: [universalScrollTo]", {"range": "3108", "text": "3109"}, "Update the dependencies array to be: [currentStep, currentStepIndex, interactWithPage, selectedTemplate]", {"range": "3110", "text": "3111"}, "Update the dependencies array to be: [currentStep, currentStepData?.overlay, currentStepIndex, interactWithPage]", {"range": "3112", "text": "3113"}, "Update the dependencies array to be: [currentStepIndex, steps, selectedTemplate, currentStep, pollForElement, setCurrentStep, setCurrentStepIndex]", {"range": "3114", "text": "3115"}, "Update the dependencies array to be: [selectedTemplate, currentStepIndex, setCurrentStep, currentStep, steps]", {"range": "3116", "text": "3117"}, "Update the dependencies array to be: [currentStepData, currentStepIndex, handleNext]", {"range": "3118", "text": "3119"}, "Update the dependencies array to be: [currentStepData, currentUrl, updateTargetAndPosition]", {"range": "3120", "text": "3121"}, {"range": "3122", "text": "3121"}, "Update the dependencies array to be: [currentStepData, currentUrl, rect, updateTargetAndPosition]", {"range": "3123", "text": "3124"}, "Update the dependencies array to be: [currentStep, selectedTemplate, selectedTemplateTour, setTooltipBackgroundcolor, setTooltipBordercolor, setTooltipBorderradius, setTooltipBordersize, setTooltipPadding, setTooltipPosition, setTooltipWidth, setTooltipXaxis, setTooltipYaxis, toolTipGuideMetaData]", {"range": "3125", "text": "3126"}, "Update the dependencies array to be: [fetchData]", {"range": "3127", "text": "3128"}, "Update the dependencies array to be: [selectedInteraction, interactions, searchTerm, filteredInteractions]", {"range": "3129", "text": "3130"}, {"range": "3131", "text": "3128"}, "Update the dependencies array to be: [checklistCheckpointListProperties, icons]", {"range": "3132", "text": "3133"}, "Update the dependencies array to be: [setImageAnchorEl, tooltip.visible]", {"range": "3134", "text": "3135"}, "Update the dependencies array to be: [rteBoxValue, boxRef, textvaluess]", {"range": "3136", "text": "3137"}, "Update the dependencies array to be: [settingAnchorEl.value, buttonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, btnidss, currentGuideId, currentStep, createWithAI, interactionData]", {"range": "3138", "text": "3139"}, [18057, 18079], "[]", [26335, 26376], "[fetchGuideDetails, hotspot, hotspotClicked]", [26858, 26871], "[designPopup, setDesignPopup]", [29595, 29742], "[isTemplateScreen, isPopupOpen, bannerPopup, currentGuideId, selectedTemplate, selectedTemplateTour, determineCurrentScreen]", [30220, 30580], "[isCollapsed, isGuideInfoScreen, isTemplateScreen, isPopupOpen, bannerPopup, currentScreen, selectedTemplate, selectedTemplateTour, guideName, currentGuideId, guideStep, htmlContent, imageSrc, buttonColor, imageName, errors]", [35188, 35222], "[savedGuideData, isSaveInProgress, currentGuideId, selectedTemplate, selectedTemplateTour, bannerPopup, setBannerPopup, setIsBannerCreationBuilderOpen, setIsAnnouncementCreationBuilderOpen, setIsTooltipCreationBuilderOpen, setIsHotspotCreationBuilderOpen, clearBannerButtonDetials, updateRTEContainerOnReload, setBannerButtonSelected, updateButtonContainerOnReload]", [70387, 70400], "[currentStep, elementSelected, handleClose, isShowIcon, setElementSelected, toolTipGuideMetaData]", [86415, 86448], "[openStepDropdown, plusIconclick, setSettingAnchorEl]", [101048, 101062], "[createWithAI, setIsUnSavedChanges, stepCreation]", [129293, 129445], "[isAnnounce<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, showB<PERSON><PERSON><PERSON>er, showTooltipenduser, showHotspotenduser, isTourTemplate]", [159065, 159122], "[currentGuide?.GuideStep, currentStep]", [164378, 164395], "[cleanupDuplicateSteps, createWithAI, currentGuideId, elementSelected, interactionData, resetHeightofBanner]", [170327, 170360], "[SetGuideName, cleanupDuplicateSteps, createWithAI, currentGuideId, currentStep, getGuideTypeValue, handlecreatefromScratchclick, interactionData, overlayEnabled, pageinteraction, progress, resetHeightofBanner, setBannerPopup, setBposition, setDismiss, setIsGuideInfoScreen, setOverlayEnabled, setPageInteraction, setProgress, setProgressColor, setSelectedOption, setSelectedTemplate, setSelectedTemplateTour, setTooltipCount, setTourDataOnEdit]", [171070, 171171], "[isAnnouncementPopupOpen, showHotspotenduser, isHotspotPopupOpen, isTourTemplate, isChecklistPreview, currentGuideId, setIsGuideInfoScreen, setSelectedTemplate, handlecreatefromScratchclick, SetGuideName, steps, selectedTemplate, getGuideTypeValue, currentStep]", [175248, 175260], "[isLoggedIn, setBannerPopup, setCurrentGuideId, setIsAnnouncementCreationBuilderOpen, setIsBannerCreationBuilderOpen, setIsGuideInfoScreen, setIsHomeScreen, setIsHotspotCreationBuilderOpen, setIsTemplateScreen, setIsTooltipCreationBuilderOpen, setIsTooltipPopup]", [196234, 196235], "", [196234, 196234], "\\", [4291, 4293], "[loggedOut]", [16077, 16132], "[getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName, setBtnName]", [17506, 17561], "[settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, targetURL]", [18006, 18008], "[selectedActions.value, targetURL]", [2347, 2349], "[isExtensionClosed, setIsExtensionClosed]", [3014, 3037], "[hasAnnouncementOpened, setDrawerActiveMenu, setDrawerSearchText, setHasAnnouncementOpened]", [3381, 3424], "[isExtensionClosed, activeMenu, searchText, setDrawerActiveMenu, setDrawerSearchText, setIsPopupOpen]", [5960, 5993], "[checkpointslistData]", [5310, 5350], "[selectedTemplate, selectedTemplateTour, setHotspotPopup, setShowLauncherSettings, setShowTooltipCanvasSettings, setTitlePopup]", [10234, 10269], "[SetElementButtonClick, createWithAI, currentGuideId, currentStep, interactionData, setButtonClick, setDropdownValue, setElementButtonName, setElementClick, setbtnidss, toolTipGuideMetaData]", [6153, 6155], "[bannerButtonSelected, buttonColor, rtesContainer, setButtonColor, textAreas, textBoxRef]", [7218, 7240], "[bannerButtonSelected, buttonColor, removeTextArea, setButtonColor, textAreas]", [7401, 7412], "[setTex<PERSON><PERSON><PERSON><PERSON>, text<PERSON><PERSON>s]", [8963, 9028], "[createWithAI, bannerButtonSelected, buttonColor, setButtonColor, textAreas]", [4728, 4730], "[OverlayValue]", "undefined", [6583, 6592], "\"undefined\"", [6719, 6726], "[getElementPosition, xpath]", [6855, 6871], "[savedGuideData, xpath]", [14985, 15050], "[textFieldProperties, imageProperties, customButton, currentStep, calculateOptimalWidth]", [19065, 19124], "[currentStep, guideStep, setOpenTooltip]", [19978, 20020], "[currentStep, isHotspotPopupOpen, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, toolTipGuideMetaData]", [20812, 20854], "[currentStep, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, showHotspotenduser, toolTipGuideMetaData]", [22789, 22884], "[toolTipGuideMetaData, elementSelected, borderSize, isALTKeywordEnabled, currentHoveredElement, rectData, selectedTemplate, selectedTemplateTour, isTooltipNameScreenOpen, isCollapsed, setCurrentHoveredElement, isGuideInfoScreen, setElementSelected, setAxisData, setTooltip, setOpenTooltip, setXpathToTooltipMetaData, createWithAI, syncAITooltipContainerData, applyHotspotProperties]", [26752, 26769], "[elementSelected, isALTKeywordEnabled, selectedTemplate, selectedTemplateTour, setIsALTKeywordEnabled]", [6220, 6243], "[stepType, currentStep, setAnnouncementPreview, setBannerPreview, setTooltipPreview, setHotspotPreview, savedGuideData?.GuideStep, setOpenTooltip, initializeTourHotspotMetadata]", [8571, 8616], "[paginationModel, activeTab, Open, accountId, fetchAnnouncements]", [8922, 8935], "[fetchAnnouncements, searchQuery]", [2651, 2653], "[setButtonProperty]", [3543, 3545], "[checkpointslistData, completedStatus]", [4387, 4413], "[selectedItem, activeItem, createWithAI, interactionData]", [4517, 4551], [5507, 5534], "[checklistGuideMetaData]", [4604, 4606], "[accountId, openSnackbar]", [17324, 17326], "[setElementSelected]", [4872, 4874], "[checklistGuideMetaData, checklistLauncherProperties, icons]", [9628, 9630], "[checklistLauncherProperties, icons, updateChecklistLauncher]", [3211, 3251], [6878, 6903], "[dismissData.Color, dismissData?.dismisssel, setDismiss]", [4631, 4686], "[settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setCurrentButtonName, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, setSelectedTab]", [6164, 6219], "[getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName]", [10327, 10340], "[currentStep, openTooltip, popupPosition, setCurrentHoveredElement, setTooltipPositionByXpath, toolTipGuideMetaData]", [27083, 27099], "[handleFocus, isRtlDirection]", [19204, 19239], "[universalScrollTo]", [20357, 20393], "[currentStep, currentStepIndex, interactWithPage, selectedTemplate]", [20804, 20848], "[currentStep, currentStepData?.overlay, currentStepIndex, interactWithPage]", [29926, 30081], "[currentStepIndex, steps, selectedTemplate, currentStep, pollForElement, setCurrentStep, setCurrentStepIndex]", [32166, 32271], "[selectedTemplate, currentStepIndex, setCurrentStep, currentStep, steps]", [33472, 33501], "[currentStepData, currentStepIndex, handleNext]", [33978, 34007], "[currentStepData, currentUrl, updateTargetAndPosition]", [34419, 34448], [34507, 34542], "[currentStepData, currentUrl, rect, updateTargetAndPosition]", [7898, 7945], "[currentStep, selectedTemplate, selectedTemplateTour, setTooltipBackgroundcolor, setTooltipBordercolor, setTooltipBorderradius, setTooltipBordersize, setTooltipPadding, setTooltipPosition, setTooltipWidth, setTooltipXaxis, setTooltipYaxis, toolTipGuideMetaData]", [7680, 7682], "[fetchData]", [4285, 4332], "[selectedInteraction, interactions, searchTerm, filteredInteractions]", [6555, 6557], [12538, 12540], "[checklistCheckpointListProperties, icons]", [10130, 10147], "[setImageAnchorEl, tooltip.visible]", [4413, 4442], "[rteBoxV<PERSON>ue, boxRef, textvaluess]", [4246, 4396], "[settingAnchorEl.value, buttonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, btnidss, currentGuideId, currentStep, createWithAI, interactionData]"]