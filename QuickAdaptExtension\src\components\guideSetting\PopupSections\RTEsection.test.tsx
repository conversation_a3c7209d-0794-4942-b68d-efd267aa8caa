import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import RTEsection from './RTEsection';
import useDrawerStore from '../../../store/drawerStore';

// Mock the store
jest.mock('../../../store/drawerStore');
const mockUseDrawerStore = useDrawerStore as jest.MockedFunction<typeof useDrawerStore>;

// Mock JoditEditor
jest.mock('jodit-react', () => {
  return function MockJoditEditor({ value, onChange, config }: any) {
    return (
      <div
        data-testid="jodit-editor"
        contentEditable
        suppressContentEditableWarning
        onInput={(e) => {
          if (onChange) {
            onChange((e.target as HTMLElement).innerHTML);
          }
        }}
        onKeyDown={(e) => {
          // Simulate the custom Enter key handling
          if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            const target = e.target as HTMLElement;
            const selection = window.getSelection();
            if (selection && selection.rangeCount > 0) {
              const range = selection.getRangeAt(0);
              const br = document.createElement('br');
              range.deleteContents();
              range.insertNode(br);
              range.setStartAfter(br);
              range.setEndAfter(br);
              range.collapse(true);
              selection.removeAllRanges();
              selection.addRange(range);
              
              if (onChange) {
                onChange(target.innerHTML);
              }
            }
          }
        }}
        dangerouslySetInnerHTML={{ __html: value || '' }}
      />
    );
  };
});

// Mock translation
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

describe('RTEsection Enter Key Functionality', () => {
  const mockProps = {
    textBoxRef: { current: null },
    guidePopUpRef: { current: null },
    isBanner: false,
    handleDeleteRTESection: jest.fn(),
    index: 0,
    onClone: jest.fn(),
    isCloneDisabled: false,
  };

  const mockStoreState = {
    rtesContainer: [
      {
        id: 'test-container-1',
        rtes: [
          {
            id: 'test-rte-1',
            text: '',
          },
        ],
      },
    ],
    updateRTEContainer: jest.fn(),
    setIsUnSavedChanges: jest.fn(),
    cloneRTEContainer: jest.fn(),
    clearRteDetails: jest.fn(),
    selectedTemplate: 'Tooltip',
    selectedTemplateTour: '',
    announcementGuideMetaData: [],
    toolTipGuideMetaData: [],
    handleAnnouncementRTEValue: jest.fn(),
    handleTooltipRTEValue: jest.fn(),
    createWithAI: false,
    currentStep: 1,
    ensureAnnouncementRTEContainer: jest.fn(),
  };

  beforeEach(() => {
    mockUseDrawerStore.mockReturnValue(mockStoreState);
    jest.clearAllMocks();
  });

  it('should handle Enter key when editor is empty', async () => {
    render(<RTEsection {...mockProps} />);
    
    const editor = screen.getByTestId('jodit-editor');
    expect(editor).toBeInTheDocument();

    // Focus the editor
    fireEvent.focus(editor);

    // Press Enter key when editor is empty
    fireEvent.keyDown(editor, { key: 'Enter', shiftKey: false });

    await waitFor(() => {
      // Check that a BR element was added
      expect(editor.innerHTML).toContain('<br>');
    });
  });

  it('should handle Enter key when there is no content before cursor', async () => {
    // Set up editor with some content but cursor at the beginning
    mockStoreState.rtesContainer[0].rtes[0].text = 'Some existing text';
    
    render(<RTEsection {...mockProps} />);
    
    const editor = screen.getByTestId('jodit-editor');
    
    // Focus the editor
    fireEvent.focus(editor);

    // Simulate cursor at the beginning of the text
    const selection = window.getSelection();
    const range = document.createRange();
    range.setStart(editor.firstChild || editor, 0);
    range.setEnd(editor.firstChild || editor, 0);
    selection?.removeAllRanges();
    selection?.addRange(range);

    // Press Enter key
    fireEvent.keyDown(editor, { key: 'Enter', shiftKey: false });

    await waitFor(() => {
      // Check that a BR element was added at the beginning
      expect(editor.innerHTML).toContain('<br>');
    });
  });

  it('should handle Enter key when there is content before cursor', async () => {
    // Set up editor with content
    mockStoreState.rtesContainer[0].rtes[0].text = 'Line 1';
    
    render(<RTEsection {...mockProps} />);
    
    const editor = screen.getByTestId('jodit-editor');
    
    // Focus the editor
    fireEvent.focus(editor);

    // Simulate cursor at the end of the text
    const selection = window.getSelection();
    const range = document.createRange();
    const textNode = editor.firstChild;
    if (textNode) {
      range.setStart(textNode, textNode.textContent?.length || 0);
      range.setEnd(textNode, textNode.textContent?.length || 0);
      selection?.removeAllRanges();
      selection?.addRange(range);
    }

    // Press Enter key
    fireEvent.keyDown(editor, { key: 'Enter', shiftKey: false });

    await waitFor(() => {
      // Check that a BR element was added
      expect(editor.innerHTML).toContain('<br>');
    });
  });

  it('should not handle Enter key when Shift is pressed', async () => {
    render(<RTEsection {...mockProps} />);
    
    const editor = screen.getByTestId('jodit-editor');
    
    // Focus the editor
    fireEvent.focus(editor);

    const initialContent = editor.innerHTML;

    // Press Shift+Enter (should not trigger custom handling)
    fireEvent.keyDown(editor, { key: 'Enter', shiftKey: true });

    // Content should remain unchanged since we prevented default behavior
    expect(editor.innerHTML).toBe(initialContent);
  });

  it('should call updateRTEContainer when content changes', async () => {
    render(<RTEsection {...mockProps} />);
    
    const editor = screen.getByTestId('jodit-editor');
    
    // Simulate content change
    fireEvent.input(editor, { target: { innerHTML: 'New content' } });

    await waitFor(() => {
      expect(mockStoreState.updateRTEContainer).toHaveBeenCalledWith(
        'test-container-1',
        'test-rte-1',
        'New content'
      );
    });
  });
});
